package rest

import (
	"net/http"

	sentrygin "github.com/getsentry/sentry-go/gin"
	"github.com/gin-contrib/gzip"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"

	"github.com/carbonrobotics/crgo/config/schema"
	httplib "github.com/carbonrobotics/crgo/http"
	"github.com/carbonrobotics/crgo/metrics"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
)

func GetREST(
	auditLogger *audit.Logger,
	schemaReader *schema.Reader,
	configCache *cache.ConfigCache,
	profileCache *cache.ProfileCache,
	streamCache *cache.StreamCache,
	environment string,
) *gin.Engine {
	// Compose Gin Engine from default cors config
	router := gin.New()
	router.Use(gin.LoggerWithWriter(gin.DefaultWriter))

	// must happen before gin recovery
	router.Use(sentrygin.New(sentrygin.Options{}))
	router.Use(httplib.HttpLogger(log.New()))
	router.Use(gin.Recovery())
	router.Use(metrics.PrometheusMiddlewareGin())

	// health and metrics endpoints
	router.GET("/metrics", metrics.HandlerGin())
	router.GET("/healthz", func(c *gin.Context) { c.String(http.StatusOK, "I am alive!") })

	// unauthed internal routes
	internal := router.Group("internal")

	// serve APIs
	internalV1 := internal.Group("v1")
	internalV1.Use(gzip.Gzip(gzip.DefaultCompression))
	RegisterProfilesRoutes(internalV1, profileCache, environment)
	RegisterReady(internalV1)
	RegisterRobotConfigRoutes(internalV1, auditLogger, configCache, streamCache)
	RegisterSchemasRoutes(internalV1, schemaReader)
	RegisterTemplatesRoutes(internalV1, auditLogger, configCache)
	RegisterUnsyncedConfigKeysRoutes(internalV1, configCache)
	return router
}
