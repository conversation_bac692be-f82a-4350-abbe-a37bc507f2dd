import json
from http import HTTPStatus
from typing import Optional

from flask import Response, jsonify, request
from pydantic import BaseModel, ConfigDict, ValidationError

from server.apis.helpers import build_validation_error_msg
from server.constants import SessionActivityState
from server.controller.experiments.predict_point_classification_controller import (
    create_experiment_session,
    get_count_of_results_in_session,
    get_session_state,
)
from server.db.queries.experiments.prediction_point_classification_sessions import PredictionPointClassificationSession
from server.routes import route
from server.types import CategoryCollectionProfile


class CreatePredictPointsClassificationRequest(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    category_collection_profile: CategoryCollectionProfile
    robot_ids: list[str]
    model_id: str


class CreatePredictPointsClassificationResponse(BaseModel):
    session_id: str
    status: Optional[SessionActivityState]

    class Config:
        use_enum_values = True


class SessionInfo(CreatePredictPointsClassificationRequest):
    session_id: str

    @classmethod
    def from_object(cls, obj: PredictionPointClassificationSession) -> "SessionInfo":
        return cls(
            session_id=obj.id,
            robot_ids=obj.robot_ids,
            model_id=obj.model_id,
            category_collection_profile=obj.category_collection_profile,
        )


class SessionStatus(BaseModel):
    session_status: SessionActivityState
    count_of_results: Optional[int] = None
    expected_count: Optional[int] = None

    class Config:
        use_enum_values = True


class GetSessionStatusResponse(BaseModel):
    session_info: SessionInfo
    session_status: SessionStatus

    class Config:
        use_enum_values = True


@route("internal", "/experiment/predict-points-classification/session", methods=["POST"])
def create_prediction_points_classification() -> Response:
    """
    Create a experiment session for the given robot IDs, model ID, and category collection profile.

    This endpoint creates a new active session if required, or retrieves the existing session if it matches the provided parameters.
    """
    data = json.loads(request.data)

    try:
        api_request = CreatePredictPointsClassificationRequest(**data)
    except ValidationError as e:
        return Response(build_validation_error_msg(e), HTTPStatus.BAD_REQUEST)
    try:
        session_id, status = create_experiment_session(
            robot_ids=api_request.robot_ids,
            model_id=api_request.model_id,
            category_collection_profile=api_request.category_collection_profile,
        )
    except ValueError as e:
        return Response(str(e), HTTPStatus.BAD_REQUEST)
    except Exception as e:
        return Response(f"Failed to create session: {str(e)}", HTTPStatus.INTERNAL_SERVER_ERROR)

    response = CreatePredictPointsClassificationResponse(
        session_id=session_id,
        status=status,
    )
    return jsonify(response.model_dump())


@route("internal", "/experiment/predict-points-classification/session/<string:session_id>", methods=["GET"])
def get_prediction_points_classification_session_status(session_id: str) -> Response:
    """
    Get the status of a long-running experiment session for the given session ID.
    """
    try:
        session, session_status = get_session_state(session_id)
        count_of_results = get_count_of_results_in_session(session_id)
    except ValueError as e:
        return Response(str(e), HTTPStatus.BAD_REQUEST)
    except Exception as e:
        return Response(f"Failed to get session status: {str(e)}", HTTPStatus.INTERNAL_SERVER_ERROR)

    response = GetSessionStatusResponse(
        session_info=SessionInfo.from_object(session),
        session_status=SessionStatus(
            session_status=session_status,
            count_of_results=count_of_results["count"],
            expected_count=session.count_points_to_load,
        ),
    )
    return jsonify(response.model_dump())
