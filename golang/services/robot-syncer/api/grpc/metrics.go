package grpc

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

var (
	grpcPanicsTotal = promauto.NewCounter(prometheus.CounterOpts{
		Name: "grpc_req_panics_recovered_total",
		Help: "Total number of gRPC requests recovered from internal panic.",
	})
	connectedStreamsPerRobot = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Name:      "connected_streams_per_robot",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "boolean of power good status",
	},
		[]string{"serial"},
	)
	robotStreamsTotal = promauto.NewGauge(prometheus.GaugeOpts{
		Name:      "robot_streams_total",
		Namespace: constants.ROSY_SERVICE_NAMESPACE,
		Help:      "Number of robot streams connected",
	})
)
