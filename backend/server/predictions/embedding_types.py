from typing import Any, Optional

from pydantic import BaseModel, ConfigDict

from server.predictions.types import DeepweedRequestType


class DeepweedEmbeddingRequest(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    model_id: str
    image_id: str
    model_s3_path: str
    image_s3_path: str
    crop_id: Optional[str] = None
    ppi: int
    message_type: str = DeepweedRequestType.EMBEDDING
    message_version: str = "v1"
    # Will use center if not provided
    x: Optional[int] = None
    y: Optional[int] = None
    metadata: dict[str, Any] = {}


class DeepweedEmbeddingResponse(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    image_id: str
    image_s3_path: str
    model_id: str
    ppi: int
    message_version: str
    message_type: str
    success: bool
    embedding: list[float]
    x: int
    y: int
    metadata: dict[str, Any]


class EmbeddingPredictionData(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    image_id: str
    model_id: str
    ppi: Optional[int] = None
    embedding: list[float]
    x: int
    y: int
    metadata: dict[str, Any]


class FewshotEmbeddingRequest(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    model_id: str
    image_id: str
    model_s3_path: str
    image_s3_path: str
    message_type: str = DeepweedRequestType.FEWSHOT_EMBEDDING
    message_version: str = "v1"
    x: int
    y: int
    metadata: dict[str, Any] = {}


class FewshotEmbeddingResponse(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    image_id: str
    image_s3_path: str
    model_id: str
    success: bool
    embedding: list[float]
    x: int
    y: int
    metadata: dict[str, Any]
    message_type: str = DeepweedRequestType.FEWSHOT_EMBEDDING
    message_version: str = "v1"


class ComparisonEmbeddingRequest(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    model_id: str
    image_id: str
    model_s3_path: str
    image_s3_path: str
    message_type: str = DeepweedRequestType.COMPARISON_EMBEDDING
    message_version: str = "v1"
    x: int
    y: int
    metadata: dict[str, Any] = {}


class ComparisonEmbeddingResponse(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    image_id: str
    image_s3_path: str
    model_id: str
    success: bool
    embedding: list[float]
    x: int
    y: int
    metadata: dict[str, Any]
    message_type: str = DeepweedRequestType.COMPARISON_EMBEDDING
    message_version: str = "v1"
