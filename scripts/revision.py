import os
import argparse
import logging

import datasets

LOG = logging.getLogger(__name__)

DATA_DIR = ".data"
DB_FILE = f"{DATA_DIR}/migration.db"
DB_URL = f"sqlite:///{DB_FILE}"


if __name__ == "__main__":
    
    parser = argparse.ArgumentParser()
    
    parser.add_argument("--dataset-type", type=str, required=True)
    parser.add_argument("--message", type=str, required=True)

    args = parser.parse_args()
    
    LOG.info("Generating migration for {args.dataset_type} with message: {args.message}")
    
    os.makedirs(DATA_DIR, exist_ok=True)
    if os.path.exists(DB_FILE):
        os.remove(DB_FILE)
    
    if args.dataset_type == "comparison":
        dataset_type = "comparison"
        dataset_lib = datasets.comparison
        
    print(DB_FILE, flush=True)
    
    dataset_lib.create(DB_FILE)
    dataset_lib.generate_migration(args.message, DB_URL, f"datasets/{dataset_type}/alembic")
