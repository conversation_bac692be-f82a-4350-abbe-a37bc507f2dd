from alembic import context
from sqlalchemy import engine_from_config, pool

from datasets.comparison.tables import Base

target_metadata = Base.metadata

config = context.config
section = config.config_ini_section

connectable = engine_from_config(
    config.get_section(config.config_ini_section),
    prefix="sqlalchemy.",
    poolclass=pool.NullPool,
)

with connectable.connect() as connection:
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        render_as_batch=True,
        compare_type=True,
    )

    with context.begin_transaction():
        context.run_migrations()
