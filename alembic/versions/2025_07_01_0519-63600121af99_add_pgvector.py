"""add-pgvector

Revision ID: 63600121af99
Revises: 25a7b61002db
Create Date: 2025-07-01 05:19:29.060445

"""
from alembic import op
import sqlalchemy as sa

import pgvector


# revision identifiers, used by Alembic.
revision = '63600121af99'
down_revision = '25a7b61002db'
branch_labels = None
depends_on = None


def upgrade():

    op.execute('CREATE EXTENSION IF NOT EXISTS vector')

    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('embeddings', schema=None) as batch_op:
        batch_op.add_column(sa.Column('data', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=True))

    with op.batch_alter_table('prediction_tags', schema=None) as batch_op:
        batch_op.drop_index('ix_prediction_tags_prediction_id')
        batch_op.drop_index('ix_prediction_tags_tag_id')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('prediction_tags', schema=None) as batch_op:
        batch_op.create_index('ix_prediction_tags_tag_id', ['tag_id'], unique=False)
        batch_op.create_index('ix_prediction_tags_prediction_id', ['prediction_id'], unique=False)

    with op.batch_alter_table('embeddings', schema=None) as batch_op:
        batch_op.drop_column('data')
    
    op.execute('DROP EXTENSION IF NOT EXISTS vector')

    # ### end Alembic commands ###
