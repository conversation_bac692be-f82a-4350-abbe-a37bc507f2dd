import logging
import os
from typing import Optional

from alembic import command
from alembic.config import Config

from .dataset import ComparisonDataset

LOG = logging.getLogger("DL Datasets")


def create(filepath: str, migration_id: Optional[str] = None) -> ComparisonDataset:

    LOG.info(f"Creating comparison dataset: {filepath}")

    if os.path.exists(filepath):
        raise RuntimeError(f"File already exists: {filepath}")

    dataset = ComparisonDataset(filepath, migration_id=migration_id)

    return dataset


def load(filepath: str, migration_id: Optional[str] = None) -> ComparisonDataset:

    LOG.info(f"Loading comparison dataset: {filepath}")

    if not os.path.exists(filepath):
        raise RuntimeError(f"File does not exist: {filepath}")

    dataset = ComparisonDataset(filepath, migration_id=migration_id)

    return dataset


def generate_migration(message: str, db_url: str, script_location: str) -> None:

    config = Config()

    config.set_main_option("script_location", script_location)
    config.set_main_option("sqlalchemy.url", db_url)
    config.set_main_option(
        "file_template", "%%(year)d_%%(month).2d_%%(day).2d_%%(hour).2d%%(minute).2d_%%(rev)s_%%(slug)s"
    )

    command.revision(config, message=message, autogenerate=True)
