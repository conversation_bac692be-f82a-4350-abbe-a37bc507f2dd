package rest

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"

	env "github.com/carbonrobotics/cloud/golang/pkg/environment"
	rosy "github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/utils"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
)

func GetProfile(profileCache ProfileCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		profileIdString := ctx.Param("profileId")
		profileId, err := utils.RequestIDToUUID(profileIdString)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid profile id")
			return
		}

		profile, ok := profileCache.GetCarbonProvidedProfile(profileId)
		if !ok {
			profile, ok = profileCache.GetCustomerProfile(profileId)
			if !ok {
				ReturnError(ctx, http.StatusNotFound, nil, "", "profile not found")
				return
			}
		}

		if profile.Deleted {
			ReturnError(ctx, http.StatusNotFound, nil, "", "profile not found")
			return
		}

		jsonData, err := json.Marshal(profile)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to marshal profile")
			return
		}

		ctx.Data(http.StatusOK, "application/json", []byte(jsonData))
	}
}

func GetProtectedProfiles(profileCache *cache.ProfileCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		profileType := ctx.DefaultQuery("profileType", "")
		profiles, err := profileCache.GetCarbonProvidedProfiles()
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to get Carbon Provided profiles")
			return
		}

		if profileType != "" {
			typeEnum, err := strconv.Atoi(profileType)
			if err != nil {
				ReturnError(ctx, http.StatusBadRequest, err, "", "invalid profile type, expected integer")
				return
			}

			profiles = profileCache.FilterProfilesByType(profiles, frontend.ProfileType(typeEnum))
		}

		jsonData, err := json.Marshal(profiles)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to marshal profiles")
			return
		}

		ctx.Data(http.StatusOK, "application/json", jsonData)
	}
}

func GetCustomerProfiles(profileCache *cache.ProfileCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		customerIdString := ctx.Param("customerId")
		customerId, err := utils.RequestIDToUUID(customerIdString)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid customer id")
			return
		}
		errorPrefix := fmt.Sprintf("failed to get customer profiles for %s", customerId)

		var profileTypeEnum *frontend.ProfileType
		var query struct {
			ProfileType *frontend.ProfileType `form:"profileType"`
		}
		if err := ctx.BindQuery(&query); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid profile type")
			return
		}
		if query.ProfileType != nil {
			profileTypeEnum = query.ProfileType
		}

		profileConfigs, err := profileCache.GetCustomerProfiles(customerId, profileTypeEnum)
		if err != nil {
			ReturnError(ctx, http.StatusNotFound, err, "", "failed to get customer profiles")
			return
		}

		jsonData, err := json.Marshal(profileConfigs)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to marshal profiles")
			return
		}

		ctx.Data(http.StatusOK, "application/json", []byte(jsonData))
	}
}

func UploadProtectedProfile(profileCache *cache.ProfileCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		profileIdString := ctx.Param("profileId")
		profileId, err := utils.RequestIDToUUID(profileIdString)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid profile id")
			return
		}

		profile := rosy.Profile{}
		if err := ctx.Bind(&profile); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid profile")
			return
		}

		if profile.Id != profileId {
			ReturnError(ctx, http.StatusBadRequest, nil, "", "profile id does not match")
			return
		}

		if !profile.Protected {
			ReturnError(ctx, http.StatusBadRequest, nil, "", "profile must be protected")
			return
		}

		if err := profileCache.SaveCarbonProvidedProfile(&profile); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to upload profile")
			return
		}

		ctx.JSON(http.StatusOK, gin.H{"success": true})
	}
}

func UploadCustomerProfile(profileCache *cache.ProfileCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		profileIdString := ctx.Param("profileId")
		profileId, err := utils.RequestIDToUUID(profileIdString)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid profile id")
			return
		}

		profile := rosy.Profile{}
		if err := ctx.Bind(&profile); err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, "", "invalid profile")
			return
		}

		if profile.Id != profileId {
			ReturnError(ctx, http.StatusBadRequest, nil, "", "profile id does not match")
			return
		}

		if profile.Protected {
			ReturnError(ctx, http.StatusBadRequest, nil, "", "customer profile must not be protected")
			return
		}

		if err := profileCache.SaveCustomerProfile(&profile); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to upload profile")
			return
		}

		ctx.JSON(http.StatusOK, gin.H{"success": true})
	}
}

func DropCustomerProfiles(profileCache *cache.ProfileCache, environment string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if environment == env.InstanceStaging.String() || environment == env.InstanceProduction.String() {
			ReturnError(ctx, http.StatusForbidden, nil, "", "dropping customer profiles is not allowed in staging or production")
			return
		}

		if err := profileCache.DropCustomerProfiles(ctx); err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, "", "failed to drop customer profiles")
			return
		}

		ctx.JSON(http.StatusOK, gin.H{"success": true})
	}
}

func RegisterProfilesRoutes(
	router *gin.RouterGroup,
	profileCache *cache.ProfileCache,
	environment string,
) {
	profiles := router.Group("/profiles")
	{
		profiles.GET("/:profileId", GetProfile(profileCache))
		profiles.GET("", GetProtectedProfiles(profileCache))
		profiles.GET("/customer/:customerId", GetCustomerProfiles(profileCache))
		profiles.POST("/protected/:profileId", UploadProtectedProfile(profileCache))
		profiles.POST("/:profileId", UploadCustomerProfile(profileCache))
		profiles.POST("/drop-customer-profiles", DropCustomerProfiles(profileCache, environment))
	}
}
