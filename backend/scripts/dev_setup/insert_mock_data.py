"""
This script is intended to be run as a development step to insert data for running veselka locally.
Invoked by the veselka `run` entrypoint script
"""

import json
import logging
import random
import string
import time
from typing import Any, Callable, Dict, Optional, ParamSpec, TypeVar, cast

import geolib.geohash
import numpy as np
from sqlalchemy import not_, null, or_
from sqlalchemy.orm.query import Query
from sqlalchemy.orm.session import Session
from sqlalchemy.sql.expression import func

from server import constants
from server.db import queries, tables
from server.db.queries import crop_translations, crops, images, point_category_translations
from server.db.queries.label_types import Confidence, Coordinates, LabelData, LineLabel, Point
from server.db.tables import (
    Categories,
    CategoriesV2,
    Crop,
    CropTranslation,
    Dataset,
    DatasetLabel,
    FurrowLabel,
    Image,
    Label,
    LabelPoint,
    Location,
    Model,
    ModelRatioMetric,
    Pipeline,
    PointCategory,
    PointCategoryTranslation,
    PointDataset,
    PointDatasetCategory,
    Prediction,
    PredictionPoint,
    Task,
    TaskV2,
    User,
)
from server.db.utils import get_session, uuid4_str
from server.utils.time import epoch_timestamp_ms

T = TypeVar("T")
P = ParamSpec("P")

rand = random.Random(0)

CROP_NAMES = [
    "Collards",
    "Radicchio",
    "Mustard",
    "Daikon",
    "Fennel",
    "Green Dandelion",
    "Red Dandelion",
    "Dill",
    "Bok Choy",
    "Red Leaf Lettuce",
    "Butter Lettuce",
    "Cilantro",
    "Radishes",
    "Tomatos",
    "Sweet Potatoes",
    "Romaine Lettuce",
    "Iceberg Lettuce",
    "Red Russian Kale",
    "Lacinato Kale",
    "Curly Kale",
    "Curly Parsley",
    "Flat Leaf Parsley",
]

FURROW_IMAGE_METADATA = [
    {
        "url": "s3://maka-pono/media/tractor1/2024-11-08/furrows_tractor1_furrow_left_2024-11-08T00-00-14-611826Z.png",
        "geo_json": """{"lla": {"lat": 33.0052351, "lng": -112.1299329, "alt": 0.0, "timestamp_ms": 1731024013199}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-11-07/furrows_tractor1_furrow_left_2024-11-07T19-21-31-478852Z.png",
        "geo_json": """{"lla": {"lat": 33.0071045, "lng": -112.1302075, "alt": 0.0, "timestamp_ms": 1731007291199}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-30/furrows_tractor1_furrow_left_2024-10-30T17-40-37-574499Z.png",
        "geo_json": """{"lla": {"lat": 33.0037306, "lng": -112.1299564, "alt": 0.0, "timestamp_ms": 1730310037000}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-29/furrows_tractor1_furrow_left_2024-10-29T15-39-19-107342Z.png",
        "geo_json": """{"lla": {"lat": 33.0069227, "lng": -112.12977769999999, "alt": 0.0, "timestamp_ms": 1730216357000}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-28/furrows_tractor1_furrow_left_2024-10-28T19-59-55-946903Z.png",
        "geo_json": """{"lla": {"lat": 33.0059152, "lng": -112.1295134, "alt": 0.0, "timestamp_ms": 1730145595200}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-28/furrows_tractor1_furrow_left_2024-10-28T20-46-53-749285Z.png",
        "geo_json": """{"lla": {"lat": 33.005349599999995, "lng": -112.12944499999999, "alt": 0.0, "timestamp_ms": 1730148412400}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-08/furrows_tractor1_furrow_left_2024-10-08T18-33-46-327492Z.png",
        "geo_json": """{"lla": {"lat": 33.4715426, "lng": -112.45114459999999, "alt": 0.0, "timestamp_ms": 1728412425000}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-29/furrows_tractor1_furrow_left_2024-10-29T15-15-34-651204Z.png",
        "geo_json": """{"lla": {"lat": 33.0070908, "lng": -112.1297778, "alt": 0.0, "timestamp_ms": 1730214933000}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-10/furrows_tractor1_furrow_left_2024-10-10T17-18-17-346434Z.png",
        "geo_json": """{"lla": {"lat": 33.4757448, "lng": -112.44788949999999, "alt": 0.0, "timestamp_ms": 1728580695000}}""",
    },
    {
        "url": "s3://maka-pono/media/tractor1/2024-10-10/furrows_tractor1_furrow_right_2024-10-10T17-23-07-303040Z.png",
        "geo_json": """{"lla": {"lat": 33.4762367, "lng": -112.4478894, "alt": 0.0, "timestamp_ms": 1728580985000}}""",
        "cam_id": "furrow_right",
    },
    {
        "url": "s3://carbon-automation-testing/furrows/rgb/tractor1/2024-12-03/furrows_tractor1_furrow_left_2024-12-03T18-53-53-837553Z.png",
        "geo_json": """{"lla": {"lat": 33.4762367, "lng": -112.4478894, "alt": 0.0, "timestamp_ms": 1728580985000}}""",
    },
    {
        "url": "s3://carbon-automation-testing/furrows/rgb/tractor1/2024-12-03/furrows_tractor1_furrow_left_2024-12-03T17-08-34-662824Z.png",
        "geo_json": """{"lla": {"lat": 33.4762367, "lng": -112.4478894, "alt": 0.0, "timestamp_ms": 1728580985000}}""",
    },
    {
        "url": "s3://carbon-automation-testing/furrows/rgb/tractor1/2024-12-03/furrows_tractor1_furrow_left_2024-12-03T01-00-31-491704Z.png",
        "geo_json": """{"lla": {"lat": 33.4762367, "lng": -112.4478894, "alt": 0.0, "timestamp_ms": 1728580985000}}""",
    },
    {
        "url": "s3://carbon-automation-testing/furrows/rgb/tractor1/2024-11-20/furrows_tractor1_furrow_left_2024-11-20T19-08-40-831604Z.png",
        "geo_json": """{"lla": {"lat": 33.4762367, "lng": -112.4478894, "alt": 0.0, "timestamp_ms": 1728580985000}}""",
    },
    {
        "url": "s3://carbon-automation-testing/furrows/rgb/tractor1/2024-12-04/furrows_tractor1_furrow_left_2024-12-04T19-14-45-591661Z.png",
        "geo_json": """{"lla": {"lat": 33.4762367, "lng": -112.4478894, "alt": 0.0, "timestamp_ms": 1728580985000}}""",
    },
]

WEEDING_IMAGE_METADATA = [
    {
        "url": "s3://maka-pono/media/slayer10/2023-02-27/a0d81b6e-9bb9-4b25-b73d-fc932e9a79b8/predict_slayer10_row2_predict2_2023-02-27T23-17-33-017000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"ambiguous_weed_count","value":3,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fit-20230125-fdbhfrsus1"}]}',
        "geo_json": '{"lla":{"lat":33.0418991,"lng":-111.9891246,"alt":0,"timestamp_ms":1677539851000},"ecef":{"x":-2003931.0325595546,"y":-4962615.159909644,"z":3457854.8603857695,"timestamp_ms":1677539851000}}',
        "city": "Maricopa,AZ",
        "geohash": "9tb7gxh5022m",
        "row_id": "row2",
        "cam_id": "predict2",
    },
    {
        "url": "s3://maka-pono/media/slayer10/2023-02-27/a0d81b6e-9bb9-4b25-b73d-fc932e9a79b8/predict_slayer10_row3_predict3_2023-02-27T23-27-52-209000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"ambiguous_weed_count","value":3,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fit-20230125-fdbhfrsus1"}]}',
        "geo_json": '{"lla":{"lat":33.0417097,"lng":-111.9891284,"alt":0,"timestamp_ms":1677540471000},"ecef":{"x":-2003935.650135281,"y":-4962625.647073147,"z":3457837.25208908,"timestamp_ms":1677540471000}}',
        "city": "Maricopa,AZ",
        "geohash": "9tb7gxh1bjj7",
        "row_id": "row3",
        "cam_id": "predict3",
    },
    {
        "url": "s3://maka-pono/media/slayer8/2023-02-27/d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b/predict_slayer8_row1_predict2_2023-02-27T21-31-42-890000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"weed_tracking","value":0.9736842105263158,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fit-20230125-fdbhfrsus1"}]}',
        "geo_json": '{"lla":{"lat":31.7872046,"lng":-107.8399771,"alt":0,"timestamp_ms":1677533501000},"ecef":{"x":-1662467.7911733673,"y":-5165595.162724267,"z":3340397.8813073514,"timestamp_ms":1677533501000}}',
        "city": "Hermanas,NM",
        "geohash": "9tekf49mfkkv",
        "row_id": "row1",
        "cam_id": "predict2",
    },
    {
        "url": "s3://maka-pono/media/slayer8/2023-02-27/d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b/predict_slayer8_row1_predict1_2023-02-27T22-20-22-096000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"weed_margin_max","value":0.9980478286743164,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fut-20230226-rqwhx86w0y"}]}',
        "geo_json": '{"lla":{"lat":31.7875834,"lng":-107.8398375,"alt":0,"timestamp_ms":1677536420000},"ecef":{"x":-1662448.4269586215,"y":-5165578.151537357,"z":3340433.5838081976,"timestamp_ms":1677536420000}}',
        "city": "Hermanas,NM",
        "geohash": "9tekf4c2jej1",
        "row_id": "row1",
        "cam_id": "predict1",
    },
    {
        "url": "s3://maka-pono/media/slayer10/2023-02-27/a0d81b6e-9bb9-4b25-b73d-fc932e9a79b8/predict_slayer10_row2_predict2_2023-02-27T22-25-54-633000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"ambiguous_weed_count","value":5,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fut-20230226-rqwhx86w0y"}]}',
        "geo_json": '{"lla":{"lat":33.0422576,"lng":-111.98912279999999,"alt":0,"timestamp_ms":1677536753000},"ecef":{"x":-2003922.7593488065,"y":-4962595.120842126,"z":3457888.1896096817,"timestamp_ms":1677536753000}}',
        "city": "Maricopa,AZ",
        "geohash": "9tb7gxhj0738",
        "row_id": "row2",
        "cam_id": "predict2",
    },
    {
        "url": "s3://maka-pono/media/slayer8/2023-02-27/d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b/predict_slayer8_row1_predict4_2023-02-27T19-26-14-756000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"weed_margin_max","value":0.9976341429057953,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fit-20230125-fdbhfrsus1"}]}',
        "geo_json": '{"lla":{"lat":31.787958099999997,"lng":-107.84023549999999,"alt":0,"timestamp_ms":1677525973000},"ecef":{"x":-1662477.603952203,"y":-5165545.769412426,"z":3340468.899735718,"timestamp_ms":1677525973000}}',
        "city": "Hermanas,NM",
        "geohash": "9tekf4c4k2uh",
        "row_id": "row1",
        "cam_id": "predict4",
    },
    {
        "url": "s3://maka-pono/media/slayer8/2023-02-28/d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b/predict_slayer8_row3_predict4_2023-02-28T00-17-11-120000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"ambiguous_weed_count","value":5,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fut-20230226-rqwhx86w0y"}]}',
        "geo_json": '{"lla":{"lat":31.7881099,"lng":-107.83958659999999,"alt":0,"timestamp_ms":1677543429000},"ecef":{"x":-1662416.3853994538,"y":-5165556.156944607,"z":3340483.207028898,"timestamp_ms":1677543429000}}',
        "city": "Hermanas,NM",
        "geohash": "9tekf4ce5t0j",
        "row_id": "row3",
        "cam_id": "predict4",
    },
    {
        "url": "s3://maka-pono/media/slayer8/2023-02-27/d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b/predict_slayer8_row1_predict4_2023-02-27T20-18-44-642000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"weed_margin_max","value":0.9980478286743164,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fut-20230226-rqwhx86w0y"}]}',
        "geo_json": '{"lla":{"lat":31.7884963,"lng":-107.8400946,"alt":0,"timestamp_ms":1677529123000},"ecef":{"x":-1662455.269950007,"y":-5165519.932472884,"z":3340519.6254888955,"timestamp_ms":1677529123000}}',
        "city": "Hermanas,NM",
        "geohash": "9tekf4cjrtjq",
        "row_id": "row1",
        "cam_id": "predict4",
    },
    {
        "url": "s3://maka-pono/media/slayer8/2023-02-27/d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b/predict_slayer8_row2_predict4_2023-02-27T20-30-55-306000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"ambiguous_weed_count","value":5,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fut-20230226-rqwhx86w0y"}]}',
        "geo_json": '{"lla":{"lat":31.7874557,"lng":-107.8400975,"alt":0,"timestamp_ms":1677529853000},"ecef":{"x":-1662474.1527278337,"y":-5165577.70782263,"z":3340421.547896749,"timestamp_ms":1677529853000}}',
        "city": "Hermanas,NM",
        "geohash": "9tekf49pre5p",
        "row_id": "row2",
        "cam_id": "predict4",
    },
    {
        "url": "s3://maka-pono/media/slayer8/2023-02-27/d5fd32ff-bc6b-48a6-b97e-a30a7ecdd96b/predict_slayer8_row1_predict1_2023-02-27T23-04-55-489000Z.png",
        "reason_json": '{"version":"1","interest_scores":[{"type":"ambiguous_weed_count","value":5,"model_url":"https://app.wandb.ai/maka-ars/deepweed/runs/fut-20230226-rqwhx86w0y"}]}',
        "geo_json": '{"lla":{"lat":31.7875248,"lng":-107.8397407,"alt":0,"timestamp_ms":1677539094000},"ecef":{"x":-1662440.7484242816,"y":-5165584.218449002,"z":3340428.060674673,"timestamp_ms":1677539094000}}',
        "city": "Hermanas,NM",
        "geohash": "9tekf49rzbj5",
        "row_id": "row1",
        "cam_id": "predict1",
    },
]

legacy_crop_name_prefix = "legacy - "
ms_in_day = 86400 * 1000


logging.basicConfig(level=logging.INFO)


def log_execution_time(func: Callable[P, T]) -> Callable[P, T]:
    def wrapper(*args: P.args, **kwargs: P.kwargs) -> T:
        start_time = time.time()
        result = func(*args, **kwargs)
        elapsed_time = time.time() - start_time
        logging.info(f"{func.__name__} took {elapsed_time:.4f} seconds to run")
        return result

    return wrapper


def format_legacy_name(name: str) -> str:
    return f"{legacy_crop_name_prefix}{name}"


def parse_plain_name_from_legacy_name(legacy_name: str) -> str:
    return legacy_name.replace(legacy_crop_name_prefix, "")


def get_weeding_images_query(db: Session) -> Query:
    return cast(Query, db.query(Image).filter(not_(Image.cam_id.ilike("furrow%"))))


def get_furrow_images_query(db: Session) -> Query:
    return cast(Query, db.query(Image).filter(Image.cam_id.ilike("furrow%")))


@log_execution_time
def insert_crops(db: Session) -> None:
    for i, crop_name in enumerate(CROP_NAMES[:10]):
        crop = Crop(
            carbon_name=crop_name,
            common_name=crop_name,
            archived=i == 0,
            configuration={},
            description=f"This is a _description_ of the crop **{crop_name}**.",
        )
        db.add(crop)

    for i, crop_name in enumerate(CROP_NAMES[10:]):
        legacy_name = format_legacy_name(crop_name)
        crop = Crop(
            carbon_name=legacy_name,
            common_name=legacy_name,
            archived=True,
            legacy_crop_name=crop_name,
            configuration={},
            description=f"This is a **legacy** crop.",
        )
        db.add(crop)

    # mock non_labelable crop
    crop = Crop(
        carbon_name="Unknown Crop",
        common_name="Unknown Crop",
        archived=False,
        configuration={"non_labelable": True},
        description=f"This crop is **unlabelable**",
    )
    db.add(crop)
    db.commit()


def create_category_data(crop: Crop) -> str:
    return json.dumps(
        {
            "crownLabels": {
                "broadleaf": {},
                "grass": {},
                "offshoot": {},
                "purslane": {},
                crop.id: {"crop": True},
            },
            "polygonLabels": {"driptape": {}},
        }
    )


@log_execution_time
def insert_categories(db: Session) -> None:
    crops: list[Crop] = db.query(Crop).all()
    for crop in crops:
        categories = Categories(data=create_category_data(crop))
        db.add(categories)
    db.commit()


@log_execution_time
def insert_categories_v2(db: Session) -> None:
    furrow_category = CategoriesV2(name="Furrow", classification_type=constants.ClassificationType.LINE)
    db.add(furrow_category)
    db.commit()


@log_execution_time
def insert_locations(db: Session) -> None:
    crops = db.query(Crop).all()
    for crop in crops:
        categories = db.query(Categories).filter(Categories.data == create_category_data(crop)).first()
        location = Location(name=crop.id, admin=0, categories=categories.id)
        db.add(location)
    db.commit()


@log_execution_time
def insert_weeding_images(db: Session) -> None:
    range_max = 20
    robot_ids = [f"slayer{i}" for i in range(range_max)]
    session_name = [f"Emergency Capture {i}" for i in range(range_max)]
    crops: list[Crop] = db.query(Crop).all()
    for i in range(500):
        # getting a random value but getting consistent values across variable fields
        # to support things like emergency captures having consistent crops
        rand_val = rand.randint(0, range_max - 1)

        real_image = rand.choice(WEEDING_IMAGE_METADATA)
        crop = crops[rand_val % len(crops)]

        admin = 0

        location_name = crop.id
        if admin:
            location_name = f"admin/{location_name}"

        location = db.query(Location).filter(Location.name == location_name).first()
        ms_in_the_past = ms_in_day * rand_val + rand.randint(0, int(ms_in_day / 2))

        # randomly decide whether this image is a part of an emergency capture
        is_session = rand_val > range_max / 2
        image_session_name = session_name[rand_val] if is_session else None
        image_capture_session_id = None
        if is_session and image_session_name:
            existing_capture_session = queries.capture_sessions.get_by_name(image_session_name)
            if existing_capture_session is not None:
                image_capture_session_id = existing_capture_session.id
            else:
                capture_session = queries.capture_sessions.create(image_session_name)
                image_capture_session_id = capture_session.id

        is_emergency = False if not is_session or (is_session and ms_in_the_past >= ms_in_day * 3) else True

        # see if captured at is over three days old. if it is, then mark as emergency else normal
        img = Image(
            priority="emergency" if is_emergency else "normal",
            location=location.id,
            robot_id=robot_ids[rand_val],
            height=3000,
            width=4096,
            ppi=200,
            captured_at=epoch_timestamp_ms() - ms_in_the_past,
            image_type="predict",
            corrected_hw=True,
            crop_id=crop.id,
            crop=crop.legacy_crop_name if crop.legacy_crop_name else None,
            focus_metric=rand.uniform(0, 1),
            capture_session_id=image_capture_session_id,
            **real_image,
        )

        db.add(img)
    db.commit()


@log_execution_time
def insert_furrow_images(db: Session) -> None:
    range_max = 20
    robot_ids = [f"tractor{i}" for i in range(range_max)]
    for i in range(500):
        # getting a random value but getting consistent values across variable fields
        # to support things like emergency captures having consistent crops
        rand_val = rand.randint(0, range_max - 1)

        real_image = rand.choice(FURROW_IMAGE_METADATA)

        ms_in_the_past = ms_in_day * rand_val + rand.randint(0, int(ms_in_day / 2))

        geo_data = json.loads(real_image["geo_json"])["lla"]

        img = Image(
            captured_at=epoch_timestamp_ms() - ms_in_the_past,
            robot_id=robot_ids[rand_val],
            priority="normal",
            height=928,
            width=1440,
            cam_id=real_image.get("cam_id", "furrow_left"),
            url=real_image["url"],
            geo_json=real_image["geo_json"],
            geohash=geolib.geohash.encode(lat=geo_data["lat"], lon=geo_data["lng"], precision=4),
            image_type=constants.ImageArtifactTypes.FURROW.value,
        )

        db.add(img)
    db.commit()


@log_execution_time
def insert_users(db: Session) -> None:
    for i in range(10):
        user = User(
            admin=rand.randint(0, 1),
            _can_review_tasks=rand.randint(0, 1),
        )

        db.add(user)
    db.commit()


@log_execution_time
def insert_weeding_tasks(db: Session) -> None:
    images = get_weeding_images_query(db).all()
    for i in range(len(images)):
        image = images[i]
        task = Task(id=image.id, valid=1 if rand.randint(0, 10) != 0 else 0, numlabels=rand.randint(0, 2))
        task.images.append(image)
        db.add(task)
    db.commit()


def create_furrow_label(
    user: User, categories: list[CategoriesV2], task: Task, image: Image, is_production: Optional[bool] = False
) -> FurrowLabel:
    category = rand.choice(categories)

    label_id = uuid4_str()
    x = rand.randint(400, 600)  # same x so that we can generate a vertical line
    line_data = LineLabel(
        category_id=category.id,
        confidence=Confidence.HIGH,
        start=Coordinates(x=x, y=rand.randint(10, 100)),
        end=Coordinates(x=x, y=rand.randint(800, 900)),
    )

    label = FurrowLabel(
        id=label_id,
        task=task,
        image=image,
        user=user,
        line_labels=[line_data.model_dump()],
        is_production=is_production,
    )
    return label


@log_execution_time
def insert_furrow_tasks(db: Session) -> None:
    images = get_furrow_images_query(db).all()
    categories = (
        db.query(CategoriesV2).filter(CategoriesV2.classification_type == constants.ClassificationType.LINE).all()
    )
    users: list[User] = db.query(User).all()
    for i in range(len(images)):
        label_task_id = uuid4_str()
        image = images[i]

        label_task = TaskV2(
            created=image.created + 10,
            updated=image.created + 10,
            id=label_task_id,
            type=constants.TaskTypes.FURROW_LABEL,
            image=image,
        )
        db.add(label_task)

        labeled = rand.choice([True, False])
        if labeled:
            label_user = rand.choice(users)
            label_task.reserved_by_user = label_user
            label_task.reserved_on = epoch_timestamp_ms() - rand.randint(int(5e3), int(5e5))
            label_task.completed_on = epoch_timestamp_ms() - rand.randint(int(4e3), int(4e5))
            label_task.is_done = True

            label = create_furrow_label(user=label_user, categories=categories, task=label_task, image=image)
            db.add(label)

            review_task_id = uuid4_str()
            review_task = TaskV2(
                id=review_task_id,
                created=label_task.completed_on + 10,
                updated=label_task.completed_on + 10,
                type=constants.TaskTypes.FURROW_REVIEW,
                image=image,
                family_id=label_task_id,
                parent_id=label_task_id,
            )
            db.add(review_task)
            db.flush()

            label_task.child_id = review_task_id

            reviewed = rand.choice([True, False])
            if reviewed:
                review_user = rand.choice(db.query(User).filter(User.id != label_user.id).all())
                review_task.reserved_by_user = review_user
                review_task.reserved_on = label_task.completed_on + rand.randint(int(5e3), int(5e5))
                review_task.completed_on = label_task.completed_on - rand.randint(int(4e3), int(4e5))
                review_task.is_done = True

                review_label = create_furrow_label(
                    user=label_user, categories=categories, task=review_task, image=image, is_production=True
                )
                db.add(review_label)
    db.commit()


def get_weeding_label_data(categories: Dict[str, Any]) -> LabelData:
    many_points = rand.randint(0, 10) == 0
    if many_points:
        num_points = rand.randint(1200, 1500)
    else:
        num_points = rand.randint(10, 200)
    data: LabelData = {"points": [], "polygons": [], "isUserDone": True, "frontend": None}
    for i in range(num_points):
        data["points"].append(
            {
                "x": rand.randint(100, 4000),
                "y": rand.randint(100, 2900),
                "id": f"{i}",
                "label": rand.choice(list(categories["crownLabels"].keys())),
                "radius": rand.randint(5, 100),
                "confidence": (Confidence.HIGH.value if rand.randint(0, 10) != 0 else Confidence.LOW.value),
            }
        )
    return data


@log_execution_time
def insert_label_points(
    db: Session, image: Optional[Image], label_id: str, points: list[Point], point_categories: list[PointCategory]
) -> None:
    for point in points:
        point_category_id = None
        for point_category in point_categories:
            if point_category.name == point["label"] or point_category.id == point["label"]:
                point_category_id = point_category.id
                break

        if point_category_id:
            label_point = LabelPoint(
                label_id=label_id,
                point_category_id=point_category_id,
                x=point["x"],
                y=point["y"],
                radius=point["radius"],
                confidence=point["confidence"],
                image_id=image.id if image else None,
                image=image,
            )
            db.add(label_point)


@log_execution_time
def insert_prediction_points(
    db: Session,
    prediction: Optional[Prediction],
    prediction_id: str,
    points: list[Point],
    point_categories: list[PointCategory],
) -> None:
    for point in points:
        point_category_id = None
        for point_category in point_categories:
            if point_category.name == point["label"] or point_category.id == point["label"]:
                point_category_id = point_category.id
                break

        if point_category_id:
            prediction_point = PredictionPoint(
                prediction_id=prediction_id,
                point_category_id=point_category_id,
                x=point["x"],
                y=point["y"],
                radius=point["radius"],
                scores={"crop": rand.uniform(0, 1), "weed": rand.uniform(0, 1)},
                prediction=prediction if prediction else None,
            )

            db.add(prediction_point)


@log_execution_time
def insert_predictions(db: Session) -> None:
    rng = np.random.default_rng(seed=42)
    for i in range(10):
        image = rng.choice(db.query(Image).all())

        data = get_weeding_label_data(categories={"crownLabels": {"crop": True, "weed": True}})

        prediction = Prediction(
            image_id=image.id if image else None,
            image=image,
            model_id=rand.choice(db.query(Model).all()).id,
            weed_point_threshold=rand.uniform(0, 1),
            crop_point_threshold=rand.uniform(0, 1),
            segmentation_threshold=rand.uniform(0, 1),
            data=data,
            done=True,
        )
        db.add(prediction)

        insert_prediction_points(db, prediction, prediction.id, data["points"], db.query(PointCategory).all())

    db.commit()


@log_execution_time
def insert_labels(db: Session) -> None:
    tasks: list[Task] = db.query(Task).all()
    users: list[User] = db.query(User).all()
    point_categories: list[PointCategory] = db.query(PointCategory).all()
    for task in tasks:
        user = rand.choice(users)
        label_done = epoch_timestamp_ms() - rand.randint(int(5e3), int(5e5))
        review_done = label_done + int(8.64e7)  # one day later

        label_id = uuid4_str()
        categories_data = json.loads(task.image.location_obj.categories_obj.data)
        legacy_compatible_categories_data = categories_data.copy()

        # support legacy mock data that reflects the state of the prod DB
        legacy_crop_name = task.image.crop_obj.legacy_crop_name
        if legacy_crop_name:
            crop_id_key: Optional[str] = None
            keys_to_modify = []

            crown_labels = categories_data["crownLabels"]
            for key in crown_labels:
                if crown_labels[key].get("crop", False):
                    crop_id_key = key
                    keys_to_modify.append(key)

            for key in keys_to_modify:
                legacy_compatible_categories_data["crownLabels"][legacy_crop_name] = legacy_compatible_categories_data[
                    "crownLabels"
                ][key]

            if crop_id_key:
                del legacy_compatible_categories_data["crownLabels"][crop_id_key]

        data: LabelData = get_weeding_label_data(legacy_compatible_categories_data)

        # Labeling
        label = Label(
            id=label_id,
            task=task.id,
            user=user.id,
            workflow=Label.WORKFLOW_LABELING,
            done=label_done,
            updated=label_done,
            data=data,
            categories=task.image.location_obj.categories_obj.id,
        )
        # Review
        if task.numlabels > 1:
            label = Label(
                id=label_id,
                task=task.id,
                user=user.id,
                workflow=Label.WORKFLOW_REVIEW,
                done=review_done,
                updated=review_done,
                data=data,
                categories=task.image.location_obj.categories_obj.id,
            )

        db.add(label)
        nullify_image_for_testing = rand.random() < 0.05
        label_point_image = None if nullify_image_for_testing else task.image
        insert_label_points(db, label_point_image, label_id, data["points"], point_categories)

    db.commit()


@log_execution_time
def insert_prelabels(db: Session) -> None:
    unlabeled_tasks = db.query(Task).filter(Task.numlabels == 0).all()
    pretrain_models = db.query(Model).filter(Model.sub_type == "pretrain").all()

    for task in unlabeled_tasks:
        if rand.choice([True, False]):
            # configuring crop such that it indicates it wants prelabels to be generated
            image = images.get(task.image.id)
            label_crop = crops.get(image.crop_id) if image and image.crop_id else None
            if label_crop:
                label_crop.configuration = {"predict-prelabel": True}
                db.add(label_crop)

            model = rand.choice(pretrain_models)
            workflow = "labeling"
            label_id = uuid4_str()

            # Note: This is just being set to utilize the get_weeding_label_data helper function.
            # It is not what should be saved and reflected in the mock DB
            categories_data = json.loads(task.image.location_obj.categories_obj.data)
            categories_data["crownLabels"] = {"prelabel": {}}
            data = get_weeding_label_data(categories_data)
            # reducing number of points so that during development, you aren't forced to spend a lot of time
            # classifying them
            data["points"] = data["points"][:5]

            label = Label(
                id=label_id,
                task=task.id,
                workflow=workflow,
                model_id=model.id,
                done=epoch_timestamp_ms() - rand.randint(int(5e3), int(5e5)),
                data=data,
                categories=task.image.location_obj.categories_obj.id,
            )
            db.add(label)
    db.commit()


@log_execution_time
def insert_datasets(db: Session) -> None:
    mock_dataset_count = 10
    for i in range(mock_dataset_count):
        dataset = Dataset(
            name=f"test{i}", train=f"train{i}.json", validation=f"validation{i}.json", test=f"test{i}.json"
        )
        db.add(dataset)
    db.commit()

    datasets = db.query(Dataset).all()

    for i in range(mock_dataset_count):
        parent_dataset = rand.choice(datasets)
        dataset = Dataset(
            name=f"test{i}",
            train=f"train{i}.json",
            validation=f"validation{i}.json",
            test=f"test{i}.json",
            parent_id=parent_dataset.id,
        )
        db.add(dataset)
    db.commit()


@log_execution_time
def insert_dataset_labels(db: Session) -> None:
    datasets = db.query(Dataset).all()
    labels = db.query(Label).all()

    for role in ["train", "validation", "test"]:
        for i in range(5):
            dataset_label = DatasetLabel(
                dataset_id=rand.choice(datasets).id, label_id=rand.choice(labels).id, role=role
            )
            db.add(dataset_label)
        db.commit()

    # make the right connections to support capture sessions
    for dataset in datasets:
        queries.capture_sessions.associate_capture_sessions_to_dataset(dataset.id)


def random_hex_color() -> str:
    """Generate a random hex color."""
    return f"#{rand.randint(0, 0xFFFFFF):06x}"


@log_execution_time
def insert_point_datasets(db: Session) -> None:
    users: list[User] = db.query(User).all()
    label_points: list[LabelPoint] = db.query(LabelPoint).limit(50).all()

    mock_point_dataset_count = 3
    for i in range(mock_point_dataset_count):
        user = rand.choice(users)
        point_dataset = PointDataset(
            name=f"Point Dataset {i}", owner_user_id=user.id if i > 0 else None, is_private=False
        )

        for j in range(rand.randint(0, 10)):
            point_dataset_category = PointDatasetCategory(name=f"category {j}", color_hex=random_hex_color())
            for label_point in rand.sample(label_points, 10):
                point_dataset_category.label_points.append(label_point)
            point_dataset.categories.append(point_dataset_category)
        db.add(point_dataset)
    db.commit()


@log_execution_time
def insert_pipelines(db: Session) -> None:
    pipeline_count = 10
    for i in range(pipeline_count):
        enabled = rand.random() < 0.7
        recommendations_enabled = rand.random() < 0.7

        crops = db.query(Crop).order_by(func.random()).limit(15).all()
        data_sources = rand.sample(crops, k=rand.randint(0, 10))
        deployments = rand.sample(crops, k=rand.randint(0, 10))

        description = f"Data Sources: {', '.join([crop.common_name for crop in data_sources])} \n Deployments: {', '.join([crop.common_name for crop in deployments])}"

        pipeline = Pipeline(
            name=f"pipeline{i}", enabled=enabled, enable_recommendation=recommendations_enabled, description=description
        )

        pipeline.data_sources.extend(data_sources)
        pipeline.deployments.extend(deployments)

        # for one entry, set the custom arguments to help with testing
        if i == pipeline_count - 1:
            pipeline.custom_arguments = {constants.PipelineCustomArgumentKey.DISABLE_CROP: True}

        db.add(pipeline)
    db.commit()


def generate_random_geohash(precision: int = 4) -> str:
    # Generate random latitude and longitude
    latitude = rand.uniform(-90, 90)
    longitude = rand.uniform(-180, 180)

    # Encode latitude and longitude into geohash
    geohash: str = geolib.geohash.encode(latitude, longitude, precision=precision)
    return geohash


@log_execution_time
def insert_models(db: Session) -> None:
    datasets_no_parent = db.query(Dataset).filter(Dataset.parent_id == null()).all()
    pipelines = db.query(Pipeline).all()
    for i in range(len(datasets_no_parent)):
        sub_type = "pretrain"
        is_stub = True
        deploy = True
        environment = rand.choice(["production", "development"])
        pipeline = rand.choice(pipelines)
        model = Model(
            id=f"prt-{uuid4_str()}",
            description=f"PRT Model Description",
            url=f"url{i}",
            version=1,
            type="deepweed",
            is_stub=is_stub,
            trained_at=(epoch_timestamp_ms() / 1000),
            environment=environment,
            deploy=deploy,
            sub_type=sub_type,
            dataset_id=datasets_no_parent[i].id,
            pipeline_id=pipeline.id,
            embedding_version=1,
            container_version="docker-container-version" if rand.random() < 0.5 else None,
        )
        db.add(model)
    db.commit()

    pretrained_models = db.query(Model).filter(Model.sub_type == "pretrain").all()
    datasets_with_parent = db.query(Dataset).filter(Dataset.parent_id != null()).all()

    for i in range(len(datasets_with_parent) // 2):
        pipeline = rand.choice(pipelines)
        parent_model = rand.choice(pretrained_models)
        sub_type = rand.choice(["full_train", "geo-fine-tune"])
        is_stub = False
        deploy = True
        environment = rand.choice(["production", "development"])
        is_geo_model = True if sub_type == "geo-fine-tune" else False
        model_id = f"fig-000{i}" if is_geo_model else f"fut-000{i}"
        model = Model(
            id=model_id,
            description=f"FIG Model Description",
            url=f"url{i}",
            version=1,
            type="deepweed",
            is_stub=is_stub,
            trained_at=(epoch_timestamp_ms() / 1000),
            environment=environment,
            deploy=deploy,
            sub_type=sub_type,
            dataset_id=datasets_with_parent[i].id,
            pipeline_id=pipeline.id,
            parent_model_id=parent_model.id,
            geohash_prefix=generate_random_geohash() if is_geo_model else None,
            embedding_version=1,
        )
        crop_name = rand.choice(CROP_NAMES)
        crop = (
            db.query(Crop)
            .filter(or_(Crop.common_name == crop_name, Crop.common_name == format_legacy_name(crop_name)))
            .first()
        )
        model.crops = [crop]

        db.add(model)
    db.commit()

    full_train_models = db.query(Model).filter(Model.sub_type == "full_train").all()

    for i in range(len(datasets_with_parent) // 2, len(datasets_with_parent)):
        pipeline = rand.choice(pipelines)
        parent_model = rand.choice(full_train_models)
        sub_type = rand.choice(["fine_tune"])
        is_stub = True
        deploy = True
        environment = rand.choice(["production", "development"])
        model = Model(
            id=f"fit-{uuid4_str()}",
            url=f"url{i}",
            description=f"FIT Model Description",
            version=1,
            type="deepweed",
            is_stub=is_stub,
            environment=environment,
            deploy=deploy,
            sub_type=sub_type,
            dataset_id=datasets_with_parent[i].id,
            pipeline_id=pipeline.id,
            parent_model_id=parent_model.id,
            embedding_version=1,
        )
        db.add(model)
    db.commit()

    num_of_p2p_models: int = 5

    for i in range(num_of_p2p_models):
        environment = rand.choice([constants.Environment.PRODUCTION, constants.Environment.DEVELOPMENT])
        model = Model(
            id=f"point-2-point-{uuid4_str()}",
            url=f"url-point-2-point{i}",
            description=f"Point 2 point models without associated pipelines",
            version=1,
            type=constants.MODEL_TYPE_P2P,
            environment=environment,
            deploy=False,
        )
        db.add(model)
    db.commit()


@log_execution_time
def insert_model_ratio_metrics(db: Session) -> None:
    models = db.query(Model).all()
    images = get_weeding_images_query(db).all()
    model = rand.choice(models)
    for i in range(50):
        image = rand.choice(images)
        mrm_crops = ModelRatioMetric(
            image_id=image.id,
            model_id=model.id,
            weed_point_threshold=0.5,
            crop_point_threshold=0.5,
            hit_distance=5,
            crop_protection_radius=1,
            metric_name="crops_targeted",
            numerator=0,
            denominator=1,
        )
        mrm_weeds = ModelRatioMetric(
            image_id=image.id,
            model_id=model.id,
            weed_point_threshold=0.5,
            crop_point_threshold=0.5,
            hit_distance=5,
            crop_protection_radius=1,
            metric_name="weeds_targeted",
            numerator=19,
            denominator=20,
        )
        db.add(mrm_crops)
        db.add(mrm_weeds)
    db.commit()


@log_execution_time
def insert_model_recommendation_history(db: Session) -> None:
    models = db.query(Model).all()
    crops = db.query(Crop).all()
    robots = [f"slayer{i}" for i in range(1, 20)]

    for _ in range(1000):
        model = rand.choice(models)
        considered_models = rand.sample(models, 10)
        crop = rand.choice(crops)
        robot_id = rand.choice(robots)
        recommendation = tables.ModelRecommendationHistory(
            robot_id=robot_id,
            model_id=model.id,
            crop_id=crop.id,
            parameters_json={"crop_id": crop.id, "robot_name": robot_id},
            results_json={
                "id": model.id,
            },
            considered_model_ids=[model.id for model in considered_models],
        )
        db.add(recommendation)
    db.commit()


@log_execution_time
def insert_classification_tasks(db: Session) -> None:
    crop = db.query(Crop).order_by(func.random()).limit(1).one()
    question_tasks_limit = 10
    dill_filters = {"limit": question_tasks_limit, "images": {"crop_id": crop.id}, "order_by": {"field": "random"}}
    question = tables.Question(
        question={"text": f"Is this {crop.common_name}?"}, filters=dill_filters, data_type="bool", valid=1
    )
    db.add(question)
    images = get_weeding_images_query(db).limit(question_tasks_limit).all()
    for image in images:
        qt = tables.QuestionTask(image=image, question=question)
        db.add(qt)
    db.commit()


@log_execution_time
def insert_crop_translations(db: Session) -> None:
    crops = db.query(Crop).all()
    for crop in crops:
        crop_translations.insert(generate_crop_translation(crop, "en", 1))
        crop_translations.insert(generate_crop_translation(crop, "es", 1))
        crop_translations.insert(generate_crop_translation(crop, "fr", 1))
        crop_translations.insert(generate_crop_translation(crop, "en", 2))
        crop_translations.insert(generate_crop_translation(crop, "es", 2))
        crop_translations.insert(generate_crop_translation(crop, "fr", 2))
    db.commit()


@log_execution_time
def insert_job_types(db: Session) -> None:
    pretrain = tables.JobType(
        name="pretrain",
        prefix="pre",
        command="horovod -np 8 python -m deeplearning.standard_jobs.scripts.pretrain",
        failure_command="python -m i.am.a.failure.script",
        node_group=constants.NodeGroup.G8g512m64c,
        priority=75,
        dl_config={},
    )

    fine_tune = tables.JobType(
        name="fine-tune",
        prefix="fit",
        command="horovod -np 8 python -m deeplearning.standard_jobs.scripts.fine_tune",
        failure_command="python -m i.am.a.failure.script",
        node_group=constants.NodeGroup.G8g256m64c,
        priority=80,
        dl_config={},
    )

    full_train = tables.JobType(
        name="full-train",
        prefix="fut",
        command="horovod -np 8 python -m deeplearning.standard_jobs.scripts.train",
        failure_command="python -m i.am.a.failure.script",
        node_group=constants.NodeGroup.G8g256m64c,
        priority=100,
        dl_config={},
    )

    no_crop_full_train = tables.JobType(
        name="no-crop-full-train",
        prefix="fut",
        command="horovod -np 8 python -m deeplearning.standard_jobs.scripts.train",
        failure_command="python -m i.am.a.failure.script",
        node_group=constants.NodeGroup.G8g256m64c,
        priority=100,
        dl_config={"no_crop": True},
    )

    db.add(pretrain)
    db.add(fine_tune)
    db.add(full_train)
    db.add(no_crop_full_train)

    job_types = [pretrain, fine_tune, full_train, no_crop_full_train]

    for pipeline in db.query(Pipeline).all():
        pipeline.job_types = rand.sample(job_types, k=rand.randint(0, 3))

    db.commit()


@log_execution_time
def insert_triggers(db: Session) -> None:
    triggers = []
    for i in range(10):
        triggers.append(
            tables.Trigger(
                name=f"Every {i} images", type=constants.TriggerType.TRAINABLE_IMAGE_INCREASE, parameters=f"{i}"
            )
        )

    every_3_days = tables.Trigger(
        name="Continuous", type=constants.TriggerType.TIME_INTERVAL_HOURS, parameters="259200"
    )
    triggers.append(every_3_days)

    oldest = tables.Trigger(name="Oldest", type=constants.TriggerType.OLDEST_PIPELINE, parameters="")
    triggers.append(oldest)

    for job_type in db.query(tables.JobType).all():
        job_type.triggers = rand.sample(triggers, k=rand.randint(0, 2))

    db.add_all(triggers)
    db.commit()


def generate_crop_translation(crop: Crop, lang_code: str, version: int) -> CropTranslation:
    name_tr = "".join(rand.choices(string.ascii_letters + string.digits, k=10))
    ct = CropTranslation(name=name_tr, language=lang_code, version=version, crop_id=crop.id)
    return ct


@log_execution_time
def insert_point_categories(db: Session) -> None:
    offshoot = tables.PointCategory(name="offshoot", display_name="Offshoot", description="", archived=False)
    broadleaf = tables.PointCategory(name="broadleaf", display_name="Broadleaf", description="", archived=False)
    grass = tables.PointCategory(name="grass", display_name="Grass", description="", archived=False)
    purslane = tables.PointCategory(name="purslane", display_name="Purslane", description="", archived=False)
    crop_category = tables.PointCategory(
        id="default-crop-category-id", name="crop", display_name="Crop", description="", archived=False
    )

    db.add_all([offshoot, broadleaf, grass, purslane, crop_category])

    crops = queries.crops.get_all()

    for crop in crops:
        crop.crop_point_category = crop_category
        crop.point_categories = [offshoot, broadleaf, grass, purslane, crop_category]

        db.add(crop)

    db.commit()


@log_execution_time
def insert_point_category_translations(db: Session) -> None:
    point_categories = db.query(PointCategory).all()
    for point_category in point_categories:
        point_category_translations.insert(generate_point_category_translation(point_category, "en", 1))
        point_category_translations.insert(generate_point_category_translation(point_category, "es", 1))
        point_category_translations.insert(generate_point_category_translation(point_category, "fr", 1))
        point_category_translations.insert(generate_point_category_translation(point_category, "en", 2))
        point_category_translations.insert(generate_point_category_translation(point_category, "es", 2))
        point_category_translations.insert(generate_point_category_translation(point_category, "fr", 2))
    db.commit()


@log_execution_time
def insert_embeddings(db: Session) -> None:
    models: list[Model] = db.query(Model).all()

    label_points: list[LabelPoint] = db.query(LabelPoint).all()

    rng = np.random.default_rng(seed=42)

    for model in models[:3]:
        for label_point in label_points[:256]:
            if rand.random() < 0.75:
                continue

            data = rng.random(1024).tolist()

            embedding = tables.Embedding(
                model_id=model.id,
                point_id=label_point.id,
                type="default",
                embedding=data,
                data=data,
            )

            db.add(embedding)

    db.commit()


def generate_point_category_translation(
    point_category: PointCategory, lang_code: str, version: int
) -> PointCategoryTranslation:
    name_tr = "".join(rand.choices(string.ascii_letters + string.digits, k=10))
    pct = PointCategoryTranslation(
        name=name_tr, language=lang_code, version=version, point_category_id=point_category.id
    )
    return pct


def main() -> None:
    db = get_session()
    print("Inserting mock data", flush=True)
    insert_crops(db)
    insert_categories(db)
    insert_categories_v2(db)
    insert_locations(db)
    insert_weeding_images(db)
    insert_furrow_images(db)
    insert_users(db)
    insert_weeding_tasks(db)
    insert_furrow_tasks(db)
    insert_point_categories(db)
    insert_labels(db)
    insert_pipelines(db)
    insert_datasets(db)
    insert_dataset_labels(db)
    insert_models(db)
    insert_predictions(db)
    insert_prelabels(db)
    insert_point_datasets(db)
    insert_model_ratio_metrics(db)
    insert_model_recommendation_history(db)
    insert_classification_tasks(db)
    insert_crop_translations(db)
    insert_job_types(db)
    insert_triggers(db)
    insert_point_category_translations(db)
    insert_embeddings(db)


if __name__ == "__main__":
    main()
