---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: veselka-cv
spec:
  replicas: 1
  revisionHistoryLimit: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app: veselka-cv
  template:
    metadata:
      labels:
        app: veselka-cv
    spec:
      imagePullSecrets:
        - name: github-registry-secret
      runtimeClassName: nvidia
      containers:
        - name: veselka-cv
          image: ghcr.io/carbonrobotics/robot/veselka_cv:master
          imagePullPolicy: Always
          command:
            - "python"
            - "veselka/cv/deepweed/server.py"
            - '[{"input_queue": "veselka-predict-queue.fifo", "environment": "production"}, {"input_queue": "veselka-batch-predict-queue.fifo", "environment": "production"}]'
          ports:
            - containerPort: 50051
          envFrom:
            - configMapRef:
                name: veselka-cv
            - secretRef:
                name: veselka-cv
          resources:
            limits:
              nvidia.com/gpu: "1"
              ephemeral-storage: "10Gi"
