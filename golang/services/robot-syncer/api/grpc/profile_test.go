package grpc

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/google/uuid"
	log "github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	rosy "github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/protos/golang/generated/proto/category_profile"
	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"
)

type testCategoryProfile struct {
	Id          string   `json:"id"`
	CustomerId  string   `json:"customerId"`
	Name        string   `json:"name"`
	CategoryIds []string `json:"categoryIds"`
}

func TestDeSerializeProfileFromProto(t *testing.T) {
	customerId := uuid.New()
	customerIdString := customerId.String()
	profileId := uuid.New()
	categoryId := uuid.New()
	timeNow := time.Now().UnixMilli()
	categoryJSON, _ := json.Marshal(&testCategoryProfile{
		Id:          profileId.String(),
		CustomerId:  customerId.String(),
		Name:        "categorytest",
		CategoryIds: []string{categoryId.String()},
	})
	strCategoryJSON := string(categoryJSON)

	tests := []struct {
		name        string
		profileReq  *robot_syncer.UploadProfileRequest
		profile     *rosy.Profile
		expectError bool
	}{
		{
			name: "category profile",
			profileReq: &robot_syncer.UploadProfileRequest{
				RobotSerial: customerId.String(),
				Profile: &robot_syncer.UploadProfileRequest_CategoryCollection{
					CategoryCollection: &category_profile.CategoryCollection{
						Id:          profileId.String(),
						CustomerId:  &customerIdString,
						Name:        "categorytest",
						CategoryIds: []string{categoryId.String()},
					},
				},
				LastUpdateTimeMs: timeNow,
			},
			profile: &rosy.Profile{
				CustomerId:  customerId,
				Id:          profileId,
				ProfileType: frontend.ProfileType_CATEGORY_COLLECTION,
				Profile:     strCategoryJSON,
				UpdatedAt:   timeNow,
				Deleted:     false,
			},
			expectError: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			profile, err := DeSerializeProfileFromProto(test.profile.CustomerId, test.profileReq)
			if err != nil {
				assert.True(t, test.expectError)
				log.Error(err)
				return
			}

			assert.NotNil(t, profile)
			assert.Equal(t, test.profile.CustomerId, profile.CustomerId)
			assert.Equal(t, test.profile.Id, profile.Id)
			assert.Equal(t, test.profile.ProfileType, profile.ProfileType)
			assert.Equal(t, test.profile.UpdatedAt, profile.UpdatedAt)
			assert.Equal(t, test.profile.Deleted, profile.Deleted)
		})
	}
}

func TestSerializeProfileToProto(t *testing.T) {
	customerId := uuid.New()
	customerIdString := customerId.String()
	profileId := uuid.New()
	categoryId := uuid.New()
	timeNow := time.Now().UnixMilli()
	categoryJSON, _ := json.Marshal(&testCategoryProfile{
		Id:          profileId.String(),
		CustomerId:  customerId.String(),
		Name:        "categorytest",
		CategoryIds: []string{categoryId.String()},
	})

	tests := []struct {
		name        string
		profile     *rosy.Profile
		profileResp *robot_syncer.GetProfileResponse
		expectError bool
	}{
		{
			name:        "empty profile",
			profile:     nil,
			profileResp: nil,
			expectError: true,
		},
		{
			name: "category profile",
			profile: &rosy.Profile{
				CustomerId:  customerId,
				Id:          profileId,
				ProfileType: frontend.ProfileType_CATEGORY_COLLECTION,
				Profile:     string(categoryJSON),
				UpdatedAt:   timeNow,
				Deleted:     false,
			},
			profileResp: &robot_syncer.GetProfileResponse{
				Profile: &robot_syncer.GetProfileResponse_CategoryCollection{
					CategoryCollection: &category_profile.CategoryCollection{
						Id:          profileId.String(),
						CustomerId:  &customerIdString,
						Name:        "categorytest",
						CategoryIds: []string{categoryId.String()},
					},
				},
			},
			expectError: false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			profileResp, err := SerializeProfileToProto(test.profile)
			if err != nil {
				assert.True(t, test.expectError)
				return
			}
			assert.NotNil(t, profileResp)
		})
	}
}
