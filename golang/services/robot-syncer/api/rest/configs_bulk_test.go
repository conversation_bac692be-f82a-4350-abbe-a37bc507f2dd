package rest

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParseSerials(t *testing.T) {
	tests := []struct {
		name          string
		serials       []string
		expectedValid int
		expectedErr   bool
	}{
		{
			name:          "empty",
			serials:       []string{},
			expectedValid: 0,
			expectedErr:   true,
		},
		{
			name:          "single valid",
			serials:       []string{"slayer123"},
			expectedValid: 1,
			expectedErr:   false,
		},
		{
			name:          "multiple same class",
			serials:       []string{"slayer123", "slayer456"},
			expectedValid: 2,
			expectedErr:   false,
		},
		{
			name:          "multiple different classes",
			serials:       []string{"slayer123", "reaper456"},
			expectedValid: 2,
			expectedErr:   false,
		},
		{
			name:          "invalid serial (assumed simulator)",
			serials:       []string{"blahblahasdf"},
			expectedValid: 1,
			expectedErr:   false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			serials, err := parseSerials(test.serials)
			if err != nil {
				assert.True(t, test.expectedErr)
			}

			assert.Equal(t, test.expectedValid, len(serials))
		})
	}
}
