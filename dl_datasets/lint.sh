#!/bin/sh

set -e # exit immediately if any command fails

# Check if we're in CI mode (non-interactive)
if [ "${DL_DATASETS_CI}" = "1" ]; then
    echo "Running in CI mode (check-only)..."
    ISORT_ARGS="--profile black --line-length 120 --ignore-whitespace --check-only --diff"
    BLACK_ARGS="-l 120 --check --diff"
    FLAKE8_ARGS="--max-line-length=120"
    MYPY_ARGS="."
else
    echo "Running in interactive mode (auto-fix)..."
    ISORT_ARGS="--profile black --line-length 120 --ignore-whitespace"
    BLACK_ARGS="-l 120"
    FLAKE8_ARGS="--max-line-length=120"
    MYPY_ARGS="."
fi

echo "Running ISORT..."
echo $PYTHONPATH
python -c "import sys; print(sys.executable)"
python -c "import sys; from pprint import pprint; pprint(sys.path)"
pip list
isort --verbose $ISORT_ARGS .
echo "ISORT Complete.\n"

echo "Running Black..."
black $BLACK_ARGS .
echo "Black Complete.\n"

echo "Running Flake8..."
flake8 $FLAKE8_ARGS .
echo "Flake8 Complete.\n"

echo "Running MyPy..."
mypy $MYPY_ARGS
echo "MyPy Complete."