package rest

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/constants"
)

var (
	bulkUpdateDurationMs = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name:      "bulk_update_duration_ms",
			Namespace: constants.ROSY_SERVICE_NAMESPACE,
			Help:      "Total time taken to perform bulk config update in ms",
		},
	)
	bulkUpdateBySerialDurationMs = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name:      "bulk_update_by_serial_duration_ms",
			Namespace: constants.ROSY_SERVICE_NAMESPACE,
			Help:      "Total time taken to perform bulk config update by serial in ms",
		},
		[]string{"serial"},
	)
	bulkUpdateNumConfigUpdatesBySerial = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name:      "bulk_update_num_config_updates_by_serial",
			Namespace: constants.ROSY_SERVICE_NAMESPACE,
			Help:      "Total number of config updates performed by serial",
		},
		[]string{"serial"},
	)
	restoreConfigToVersionDurationMs = promauto.NewGauge(
		prometheus.GaugeOpts{
			Name:      "restore_config_to_version_duration_ms",
			Namespace: constants.ROSY_SERVICE_NAMESPACE,
			Help:      "Total time taken to perform config restore to version in ms",
		},
	)
)
