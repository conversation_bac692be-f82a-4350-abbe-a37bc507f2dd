services:
  veselka-dev:
    build:
      context: .
      dockerfile: Dockerfile
    image: veselka:dev
    container_name: veselka-dev
    profiles:
      - dev
    networks:
      - veselka_net
    env_file:
      - .env
    environment:
      - DB_CONNECTION=${DB_CONNECTION:-*****************************************/test}
      - REDIS_URL=${REDIS_URL:-redis://veselka-redis:6379}
      - LOCAL_RUN=1
      - GENERATE_MOCK_DATA=1
      - AWS_DEFAULT_REGION=us-west-2
      - AWS_SHARED_CREDENTIALS_FILE=/.aws/credentials
      - VESELKA_IMAGE_SERVICE_URL=${VESELKA_IMAGE_SERVICE_URL:-host.docker.internal:9000}
    volumes:
      - ${HOME}/.aws:/.aws:ro
      - ./backend:/veselka/backend
      - ./alembic:/veselka/alembic
      - ./alembic.ini:/veselka/alembic.ini
      - ./pyproject.toml:/veselka/pyproject.toml
      - ./mypy.ini:/veselka/mypy.ini
      - ./.vscode:/veselka/.vscode
    ports:
      - "8081:8081"
    depends_on:
      veselka-db:
        condition: service_healthy
      veselka-redis:
        condition: service_started
      veselka-celery:
        condition: service_started

  veselka-redis:
    image: 'redis:4.0-alpine'
    container_name: veselka-redis
    profiles:
      - dev
      - debugger
    networks:
      - veselka_net
    ports:
      - "6379:6379"

  veselka-redis-test:
    image: 'redis:4.0-alpine'
    container_name: veselka-redis-test
    networks:
      - veselka_test_net
    profiles:
      - test
    ports:
      - '6378:6379'
    command: redis-server
    healthcheck:
        test: ["CMD", "redis-cli", "ping"]
        interval: 10s
        timeout: 5s
        retries: 3

  veselka-db:
    build:
      context: .
      dockerfile: Dockerfile.postgres
    container_name: veselka-db
    profiles:
      - dev
      - debugger
      - analytics
    networks:
      - veselka_net
    env_file:
      - .env
    environment:
      POSTGRES_PASSWORD: testing
      POSTGRES_USER: test
    ports:
      - "${DB_PORT:-5432}:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test -h 127.0.0.1"]
      interval: 10s
      timeout: 5s
      retries: 5

  veselka-db-test:
    build:
      context: .
      dockerfile: Dockerfile.postgres
    container_name: veselka-db-test
    networks:
      - veselka_test_net
    profiles: 
      - test
    ports:
      - '5431:5432'
    environment:
      POSTGRES_USER: 'veselka_test'
      POSTGRES_PASSWORD: 'password'

  veselka-celery:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: veselka-celery
    networks:
      - veselka_net
    profiles:
      - dev
      - debugger
    env_file:
      - .env
    environment:
      - DB_CONNECTION=${DB_CONNECTION:-*****************************************/test}
      - REDIS_URL=${REDIS_URL:-redis://veselka-redis:6379}
      - CELERY_CONCURRENCY=${CELERY_CONCURRENCY:-2}
      - AWS_DEFAULT_REGION=us-west-2
      - AWS_SHARED_CREDENTIALS_FILE=/.aws/credentials
      - VESELKA_IMAGE_SERVICE_URL=${VESELKA_IMAGE_SERVICE_URL:-host.docker.internal:9000}
    volumes:
      - ${HOME}/.aws:/.aws:ro
      - ./backend:/veselka/backend
      - ./alembic:/veselka/alembic
      - ./pyproject.toml:/veselka/pyproject.toml
      - ./mypy.ini:/veselka/mypy.ini
      - ./.vscode:/veselka/.vscode
    command: celery --app=server.tasks.app worker --loglevel=INFO -Q celery,cleanup,task_v2_furrows,predictions,datasets,model_ratio_metrics,model_recommendation

  veselka-analytics:
    build:
      context: .
      dockerfile: Dockerfile.analytics
    image: veselka-analytics:latest
    container_name: veselka-analytics
    profiles:
      - dev
      - analytics
    networks:
      - veselka_net
    environment:
      - DB_CONNECTION=${DB_CONNECTION:-*****************************************/test}
      - REDIS_URL=${REDIS_URL:-redis://veselka-redis:6379}
      - CELERY_CONCURRENCY=${CELERY_CONCURRENCY:-2}
      - AWS_DEFAULT_REGION=us-west-2
      - AWS_SHARED_CREDENTIALS_FILE=/.aws/credentials
      - VESELKA_IMAGE_SERVICE_URL=${VESELKA_IMAGE_SERVICE_URL:-host.docker.internal:9000}
      - MAPBOX_API_KEY=${MAPBOX_API_KEY}
      - NAMESPACE=testing
      - ANALYTICS_MODE=development
    volumes:
      - ${HOME}/.aws:/.aws:ro
      - ./backend:/veselka/backend
      - ./.vscode:/veselka/.vscode
    ports:
      - "5006:5006"
    depends_on:
      - veselka-db
    command: python /veselka/backend/analytics/run.py --local-websockets

  veselka-unit-test:
    image: veselka:dev
    container_name: veselka-unit-test
    profiles: 
      - test
    environment:
      VESELKA_MODE: test
    volumes:
      - ./:/veselka
      - /veselka/backend/server/generated
    command: pytest -vv -rf /veselka/backend/test/

  veselka-integration-test:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: veselka-integration-test
    profiles: 
        - test
    networks:
        - veselka_test_net
    environment:
        - VESELKA_MODE=test
        - REDIS_URL=redis://veselka-redis-test:6379
        - VESELKA_PREDICT_RESULT_QUEUE=${VESELKA_PREDICT_RESULT_QUEUE:-veselka-predict-result-queue.fifo}
        - DB_CONNECTION=*******************************************************/veselka_test
        - GIT_SHA=${GIT_SHA:-unknown}
    volumes:
        - ./:/veselka
    command: >
        sh -c "alembic -c /veselka/alembic.ini upgrade head && pytest -vv -rf /veselka/backend/test_integration"
    depends_on:
      - veselka-redis-test
      - veselka-db-test

networks:
  veselka_net:
    driver: bridge
  veselka_test_net:
    driver: bridge
