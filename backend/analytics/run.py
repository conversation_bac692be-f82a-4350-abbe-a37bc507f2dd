import argparse
import logging
from typing import Callable

import panel

from analytics.pages import (
    bad_label_reviewer,
    bad_labels_explorer,
    blurry_images,
    category_profile_viewer,
    chip_charts,
    config_checker,
    cross_correlation_viewer,
    daily_image_reports,
    embeddings,
    episodic_evaluation,
    image_color_analysis,
    image_partitions,
    job_creator_logs_explorer,
    model_issues,
    quick_image_viewer,
    similarity_search,
    two_random_points,
)

LOG = logging.getLogger(__name__)

ROUTES: dict[str, Callable[[], panel.Column]] = {
    "image_color_analysis": image_color_analysis.get_page,
    "bad_labels_explorer": bad_labels_explorer.get_page,
    "bad_label_reviewer": bad_label_reviewer.get_page,
    "config_checker": config_checker.get_page,
    "model_issues": model_issues.get_page,
    "daily_image_reports": daily_image_reports.get_page,
    "embeddings": embeddings.get_page,
    "job_creator_logs_explorer": job_creator_logs_explorer.get_page,
    "image_partitions": image_partitions.get_page,
    "episodic_evaluations": episodic_evaluation.get_page,
    "quick_image_viewer": quick_image_viewer.get_page,
    "category_profile_viewer": category_profile_viewer.get_page,
    "blurry_images": blurry_images.get_page,
    "chip_charts": chip_charts.get_page,
    "cross_correlation_viewer": cross_correlation_viewer.get_page,
    "similarity_search": similarity_search.get_page,
    "two_random_points": two_random_points.get_page,
}
panel.extension(nthreads=8)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--local-websockets", action="store_true")

    parser.set_defaults(local_websockets=False)

    args = parser.parse_args()

    logging.basicConfig()
    logging.getLogger().setLevel(logging.INFO)

    if args.local_websockets:
        websocket_origin = ["localhost:5006"]
    else:
        websocket_origin = ["*.cloud.carbonrobotics.com"]

    LOG.info("Starting Carbon Analytics")

    panel.serve(
        panels=ROUTES,
        titles={k: k.replace("_", " ").title() for k in ROUTES.keys()},
        port=5006,
        liveness=True,
        autoreload=True,
        admin=True,
        title="Carbon Analytics",
        websocket_origin=websocket_origin,
    )
