package grpc

import (
	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/recovery"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"runtime/debug"
	"time"

	"github.com/authzed/grpcutil"
	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	grpcprom "github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/reflection"

	httplib "github.com/carbonrobotics/crgo/http"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/clients/portal"

	"github.com/carbonrobotics/cloud/golang/pkg/auth/middleware"
)

/**
 * There are at most n clients to the RoSy gRPC api at any given time; n being
 * the number of robots in the fleet.
 *
 * Config changes made on the robot will be sent to RoSy via Unary GRPC messages.
 * Config changes made via RoSy cannot be sent to the robot via Unary GRPC
 * messages as robots have dynamic IP addresses. Instead, the robot opens a
 * gRPC stream, subscribing to RoSy's ConfigNotificationService. When a config
 * change is made via RoSy, RoSy sends a notification to the robot via the
 * stream. The robot can then request the updated config tree using the Key
 * provided in the notification and update its stored value.
 *
 * To track the state of the robot's connection, RoSy will maintain a map of
 * robot serials to gRPC streams. When a robot connects, RoSy will add the
 * stream to the map. When a robot disconnects, the stream is removed. We are
 * able to use this map to determine which robots are online and able to
 * receive config updates.
 **/

func GetGRPC(
	domain string,
	audience string,
	auditLogger *audit.Logger,
	portalClient *portal.Client,
	configCache *cache.ConfigCache,
	profileCache *cache.ProfileCache,
	streamCache *cache.StreamCache,
) *grpc.Server {
	srvMetrics := grpcprom.NewServerMetrics(
		grpcprom.WithServerHandlingTimeHistogram(
			grpcprom.WithHistogramBuckets([]float64{0.001, 0.01, 0.1, 0.3, 0.6, 1, 3, 6, 9, 20, 30, 60, 90, 120}),
		),
	)
	prometheus.MustRegister(srvMetrics)

	keepAliveParams := grpc.KeepaliveParams(keepalive.ServerParameters{
		Time:                  2 * time.Minute,  // Ping the client every 2 minutes if there's no activity
		Timeout:               60 * time.Second, // Timeout after 60 seconds if the client doesn't respond to ping
		MaxConnectionIdle:     10 * time.Minute, // Max idle time before sending a ping
		MaxConnectionAge:      30 * time.Minute, // Max age of a connection before forcing a shutdown
		MaxConnectionAgeGrace: 5 * time.Minute,  // Grace period after MaxConnectionAge before terminating
	})

	grpcPanicRecoveryHandler := func(p any) (err error) {
		grpcPanicsTotal.Inc()
		logrus.Debug("msg", "recovered from panic", "panic", p, "stack", string(debug.Stack()))
		return status.Errorf(codes.Internal, "%s", p)
	}

	authValidator := middleware.Auth0JWTValidatingGRPC(domain, audience)

	server := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			srvMetrics.UnaryServerInterceptor(),
			httplib.GrpcLogger,
			grpc_auth.UnaryServerInterceptor(authValidator),
			recovery.UnaryServerInterceptor(recovery.WithRecoveryHandler(grpcPanicRecoveryHandler)),
		),

		grpc.ChainStreamInterceptor(
			srvMetrics.StreamServerInterceptor(),
			grpc_auth.StreamServerInterceptor(authValidator),
			recovery.StreamServerInterceptor(recovery.WithRecoveryHandler(grpcPanicRecoveryHandler)),
		),
		keepAliveParams,
	)

	srvMetrics.InitializeMetrics(server)

	reflection.Register(grpcutil.NewAuthlessReflectionInterceptor(server))
	healthServer := grpcutil.NewAuthlessHealthServer()
	grpc_health_v1.RegisterHealthServer(server, healthServer)

	NewConfigService(server, auditLogger, configCache, streamCache)
	NewConfigNotificationService(server, streamCache, configCache)
	NewProfileSyncService(server, portalClient, profileCache)

	return server
}
