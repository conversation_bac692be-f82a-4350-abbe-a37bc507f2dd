import panel as pn
from typing import Optional

from server.db import queries


class Page:
    def __init__(self) -> None:
        self.point_id_input = pn.widgets.TextInput(
            placeholder="Enter point ID...",
            sizing_mode="stretch_width"
        )
        self.search_point_button = pn.widgets.Button(
            name="Search Point",
            button_type="primary",
            sizing_mode="stretch_width"
        )

        self.model_id_input = pn.widgets.TextInput(
            placeholder="Enter model ID...",
            sizing_mode="stretch_width"
        )
        self.search_model_button = pn.widgets.Button(
            name="Search Model",
            button_type="primary",
            sizing_mode="stretch_width"
        )

        # Bind the search functions
        self.search_point_results = pn.bind(self.search_point_by_id, self.point_id_input, watch=False)
        self.search_model_results = pn.bind(self.search_model_by_id, self.model_id_input, watch=False)

        # Connect button clicks to trigger searches
        self.search_point_button.on_click(self._trigger_point_search)
        self.search_model_button.on_click(self._trigger_model_search)

    def _trigger_point_search(self, event):
        """Trigger the point search when button is clicked."""
        self.search_point_results.trigger()

    def _trigger_model_search(self, event):
        """Trigger the model search when button is clicked."""
        self.search_model_results.trigger()

    def search_point_by_id(self, point_id: str) -> pn.Column:
        """Search for a point by ID in prediction points first, then label points."""
        if not point_id.strip():
            return pn.Column(pn.pane.Markdown("Please enter a point ID."))

        # First try prediction points
        prediction_point = queries.prediction_points.get(point_id)
        if prediction_point:
            return pn.Column(
                pn.pane.Markdown("## Prediction Point Found"),
                pn.pane.Markdown(f"**Point ID:** {prediction_point.id}"),
                pn.pane.Markdown(f"**Position:** ({prediction_point.x}, {prediction_point.y})"),
                pn.pane.Markdown(f"**Radius:** {prediction_point.radius}"),
                pn.pane.Markdown(f"**Prediction ID:** {prediction_point.prediction_id}"),
                pn.pane.Markdown(f"**Point Category ID:** {prediction_point.point_category_id}"),
                pn.pane.Markdown(f"**Scores:** {prediction_point.scores}"),
                sizing_mode="stretch_width"
            )

        # If not found in prediction points, try label points
        label_point = queries.label_points.get(point_id)
        if label_point:
            return pn.Column(
                pn.pane.Markdown("## Label Point Found"),
                pn.pane.Markdown(f"**Point ID:** {label_point.id}"),
                pn.pane.Markdown(f"**Position:** ({label_point.x}, {label_point.y})"),
                pn.pane.Markdown(f"**Radius:** {label_point.radius}"),
                pn.pane.Markdown(f"**Confidence:** {label_point.confidence}"),
                pn.pane.Markdown(f"**Label ID:** {label_point.label_id}"),
                pn.pane.Markdown(f"**Image ID:** {label_point.image_id}"),
                pn.pane.Markdown(f"**Point Category ID:** {label_point.point_category_id}"),
                sizing_mode="stretch_width"
            )

        # Point not found in either table
        return pn.Column(
            pn.pane.Markdown("## Point Not Found"),
            pn.pane.Markdown(f"Could not find a point with ID: `{point_id}`"),
            pn.pane.Markdown("Please check the point ID and try again."),
            sizing_mode="stretch_width"
        )

    def search_model_by_id(self, model_id: str) -> pn.Column:
        """Search for a model by ID in the models table."""
        if not model_id.strip():
            return pn.Column(pn.pane.Markdown("Please enter a model ID."))

        # Search for the model
        model = queries.models.get(model_id)
        if model:
            return pn.Column(
                pn.pane.Markdown("## Model Found"),
                pn.pane.Markdown(f"**Model ID:** {model.id}"),
                pn.pane.Markdown(f"**Name:** {model.name}"),
                pn.pane.Markdown(f"**Type:** {model.type}"),
                pn.pane.Markdown(f"**Version:** {model.version}"),
                pn.pane.Markdown(f"**Status:** {model.status}"),
                pn.pane.Markdown(f"**Created:** {model.created}"),
                pn.pane.Markdown(f"**Updated:** {model.updated}"),
                sizing_mode="stretch_width"
            )

        # Model not found
        return pn.Column(
            pn.pane.Markdown("## Model Not Found"),
            pn.pane.Markdown(f"Could not find a model with ID: `{model_id}`"),
            pn.pane.Markdown("Please check the model ID and try again."),
            sizing_mode="stretch_width"
        )

    def render(self) -> pn.Column:
        """Main page function for similarity search."""
        pn.extension(loading_spinner="dots", loading_color="#1e52d6")

        # Layout
        page = pn.Column(
            pn.Spacer(max_height=50, sizing_mode="stretch_both"),
            pn.Row(
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                pn.Column(
                    pn.pane.Markdown("# Similarity Search"),
                    pn.pane.Markdown(
                        """
                        Search for points by ID or find similar embeddings.
                        """
                    ),
                    # Point ID search section
                    pn.pane.Markdown("## Search Point by ID"),
                    pn.pane.Markdown("Enter a point ID to search in prediction points and label points:"),
                    pn.Row(
                        self.point_id_input,
                        self.search_button,
                        sizing_mode="stretch_width"
                    ),
                    self.search_results,
                    pn.Spacer(height=30),
                    # Future similarity search section
                    pn.pane.Markdown("## Similarity Search"),
                    pn.pane.Markdown(
                        """
                        **Coming soon:**
                        - Search by embedding vectors
                        - Find nearest neighbors
                        - Filter by similarity threshold
                        - Display results with links to Veselka
                        """
                    ),
                    sizing_mode="stretch_width",
                    max_width=1600,
                    min_width=360,
                ),
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                sizing_mode="stretch_width",
                align=("center", "center"),
            ),
        )

        return page


def get_page() -> pn.Column:
    page = Page()

    return pn.Column(
        page.render(),
        sizing_mode="stretch_width",
        width_policy="max",
    )