import json
import logging
from typing import Any, Dict, Optional

from flask import jsonify, request
from pydantic import BaseModel, ConfigDict
from werkzeug import Response

from server import auth, config, constants, slack
from server.apis.frontend.helpers import required_response
from server.auth import current_user
from server.db.queries import crops, images, labels, tasks, users
from server.db.tables import Crop, Label, Task, User
from server.routes import route
from server.workflow import next_task

LOG = logging.getLogger(__name__)


@route("frontend", "/tasks/status", methods=["GET"])
def get_tasks_status() -> Response:
    """
    Provides counts for how many tasks need to be worked on
        Returns:
            {
                - "needs_labeling": int - number of tasks available to be labeled by user,
                - "needs_reviewing": int - number of tasks available to be reviewed by user,
            }
    """
    user_id: Optional[str] = auth.current_user.id
    task_stats = tasks.available_task_stats(user_id)
    return jsonify(task_stats.model_dump())


class TaskDetectionResponseCrop(BaseModel):
    id: str
    common_name: str
    description: str
    labeling_instructions_url: Optional[str]


class TaskDetectionResponseTask(BaseModel):
    id: str
    invalidated_reason: Optional[int]
    valid: int


class TaskDetectionResponse(BaseModel):
    crop: TaskDetectionResponseCrop
    task: TaskDetectionResponseTask
    image: dict[str, Any]
    label: dict[str, Any]


class HistoricalLabel(BaseModel):
    model_config = ConfigDict(
        protected_namespaces=()
    )  # Disable protected namespace warnings that would yell at us for using `model_id`

    id: str
    type: str
    created_on: int
    updated_on: int
    invalidated_by_user: Optional[dict[str, Any]]
    is_done: bool
    completed_on: int
    reserved_by_user: dict[str, Any]
    model_id: Optional[str]


@route("frontend", "/task/detection", methods=["GET"])
def get_labeling_task() -> Response:
    """
    Gets the next available Deepweed detection task for a user
    Arguments:
    {
        - "task_id": str - Optional - can be used to specify which task they want to label instead of an automatic assignment
    }

    Returns:
        - Response:
            {
                -  "crop": {
                    - "id": guid - crop id,
                    - "common_name": str - human readable name for the crop in English,
                    - "description": Optional - str - generic description about what the crop is and key identifiable features,
                    - "labeling_instructions_url": Optional - str - a link that the labelers can reference for instructions on how to properly label images of this crop
                }
                -  "task": {
                    - "id": guid - task id,
                }
                -  "image": {
                    - "id": guid - image id,
                    - "ppcm": int - how many pixels in the image map to one centimeter,
                    - "categories": {
                        crownLabels: {
                         - [classificationId: guid | string ]: { crop: boolean } - all of the available bounding circle classification options
                        },
                        polygonLabels: {
                         - [classificationId: string]: { crop: boolean } - all of the available polygon classification options
                        }
                    }
                }
                - "label": {
                    - "id": guid - Optional - label id
                    - "labeled_by_model": bool - Optional - whether the label has been prelabeled
                    - "points": Point[] - Optional - the previous label's point information
                    - "polygons": Polygon[] - Optional - the previous label's polygon information
                    - "workflow": Workflow - Optional - what workflow the current label is accomplishing
                    - "history": [{
                        - "id": str - the id of the label
                        - "type": Workflow - what workflow the label was for
                        - "created_on": int - date the label was created
                        - "updated_on": int - date the label was last updated
                        - "invalidated_by_user" - Optional - {
                            - "id": guid - user id
                            - "email": str - user's email address
                        }
                        - "is_done": bool - whether the label was completed or not
                        - "completed_on": int - date when the label was completed
                        - "reserved_by_user": - Optional - {
                            - "id": guid - user id
                            - "email": str - user's email address
                        } - information about who completed this label
                        - "model_id": str - Optional - the model id if the label was generated by a model
                    }] - information about past labels
                }
            }
    """
    task_id: Optional[str] = request.args.get("task_id", None, type=str)

    user: Optional[User] = users.get(auth.current_user.id)

    if not user:
        return Response(f"Invalid User", status=401)

    is_admin = user.admin == 1
    task: Optional[Task] = None
    label: Optional[Label] = None
    workflow: Optional[str] = None

    if task_id:
        task = tasks.get(task_id)
        if not task:
            return Response(f'Task "{task_id}" was not found', status=404)

        label = labels.get_last_label_for_task(task.id)
        is_label_in_the_past = False
        if label and (
            label.done
            or (
                (label.workflow == Label.WORKFLOW_LABELING and task.numlabels > Task.NUM_LABELS_LABELING)
                or (label.workflow == Label.WORKFLOW_REVIEW and task.numlabels > Task.NUM_LABELS_REVIEW)
            )
        ):
            # the label that's being asked for is not a good candidate for labeling because it is already done
            # or a different label has progressed the task along such that this label is no longer relevant
            is_label_in_the_past = True
        if not is_admin and (label == None or is_label_in_the_past):
            LOG.info(
                f"Non admin user {user.id} attempted to fetch a task that they should not be requesting {task_id}. Numlabels {task.numlabels}. Label workflow {label.workflow if label else 'No Label'}"
            )
            return Response(
                f"Unauthorized Request. Non admin users can not request historical, outdated, or non system assigned tasks by task ids. Hit 'Refetch Next Label Assignment' (↻) to fetch a different task.",
                status=401,
            )
    else:
        workflow = (
            Label.WORKFLOW_REVIEW
            if user.can_review_tasks and tasks.available_review_count(user) > 0
            else Label.WORKFLOW_LABELING
        )
        task, label, _resumed = next_task(user=user, workflow=workflow)

        if not task:
            LOG.warning(f'There are no images left that are available to label for the workflow "{workflow}"')
            return Response("There are no images left that are available to label", status=404)

    image = images.get(task.image.id)
    if not image:
        return Response(f"The image associated with the requested task was not found", status=500)

    crop: Optional[Crop] = crops.get(image.crop_id)

    if crop is None:
        raise RuntimeError(f"The crop associated with the image was not found. Image ID: {image.id}")

    historical_labels = labels.get_complete_labels_for_task(task.id, False)

    history = []
    for historical_label in historical_labels:
        if historical_label.reject_user_id:
            invalidated_by_user = {
                "id": historical_label.reject_user_id,
                "email": users.lookup_user_email(historical_label.reject_user_id),
            }
        else:
            invalidated_by_user = None

        reserved_by_user = {
            "id": historical_label.user,
            "email": users.lookup_user_email(historical_label.user),
        }

        history.append(
            HistoricalLabel(
                id=historical_label.id,
                type=historical_label.workflow,
                created_on=historical_label.created,
                updated_on=historical_label.updated,
                invalidated_by_user=invalidated_by_user,
                is_done=bool(historical_label.done),
                completed_on=historical_label.done,
                reserved_by_user=reserved_by_user,
                model_id=historical_label.model_id,
            )
        )

    history = sorted(history, key=lambda x: x.completed_on)

    # Create response
    resp: Dict[str, Any] = {
        "crop": TaskDetectionResponseCrop(
            id=crop.id,
            common_name=crop.common_name,
            description=crop.description,
            labeling_instructions_url=crop.labeling_instructions_url,
        ),
        "task": TaskDetectionResponseTask(
            id=task.id,
            invalidated_reason=task.invalidated_reason,
            valid=task.valid,
        ),
        "image": image.to_dict(["categories", "id", "ppcm"]),
        "label": {},
    }

    resp["image"]["categories"] = json.loads(resp["image"]["categories"])

    if label and label.data:
        for point in label.data.get("points", []):
            # Handle converting from legacy numerical id format to current expected string format
            if point.get("id", None) is not None:
                point["id"] = str(point["id"])
            # if we are presenting legacy data, convert if possible to the new format so that
            # all new data is created in the new standard
            # Note: this conversion is needed otherwise we will have a category option mismatch since all active category options point to crop ids
            if crop and crop.legacy_crop_name and point["label"] == crop.legacy_crop_name:
                point["label"] = crop.id
        for polygon in label.data.get("polygons", []):
            # Handle converting from legacy numerical id format to current expected string format
            if polygon.get("id", None) is not None:
                polygon["id"] = str(polygon["id"])

    # label exists if there are any previous labels like Labeling on an image with prelabels, or reviewing on a previously labeled image
    if label:
        resp["label"] = label.to_dict(["id", "labeled_by_model", "points", "polygons", "workflow"])

    # there can be historical information even if there isn't a current valid previous label
    resp["label"]["history"] = [historical_label.model_dump() for historical_label in history]

    # if a particular task id was specified by an admin, allow them to label the image (outside of the reservation system)
    # if the label already exists, they are only allowed to make more labels (reviewing must go through normal processes)
    if task_id and is_admin:
        resp["label"] = {"workflow": Label.WORKFLOW_LABELING, "history": resp["label"]["history"]}

    LOG.info(
        f'Assigning next task to {user} -> Workflow: {label.workflow if label else workflow} Task: {task.id} Label: {label.id if label else "NONE"}'
    )

    response = TaskDetectionResponse(
        crop=resp["crop"],
        task=resp["task"],
        image=resp["image"],
        label=resp["label"],
    )

    return jsonify(response.model_dump())


@route("frontend", "/task/invalidate", methods=["PUT"])
def invalidate_task() -> Response:
    """Allows the user to skip a task, by invalidating it, without annotating it

    Arguments:
        {
            - "task_id": str - Required - Task id for the task that needs to be skipped (Required)
            - "invalidated_reason": int - Required - An code mapping to an explanation for why the task was skipped
        }

    Returns:
        - Response: bool - returns true if the task was successfully skipped
    """
    task_id = str(request.args.get("task_id", None))
    invalidated_reason = request.args.get("invalidated_reason", type=int)

    if not task_id:
        return required_response("task_id")
    if not invalidated_reason:
        return required_response("invalidated_reason")

    task = tasks.get(task_id)
    if not task:
        return Response(f'Task "{task_id}" was not found', status=404)

    is_valid = constants.InvalidationReason.validate(invalidated_reason)
    if not is_valid:
        return Response(f'Reason with a value of "{invalidated_reason}" is not supported', 400)

    # setting the task as invalid
    tasks.invalidate(task, current_user.id, invalidated_reason)

    if constants.InvalidationReason.WrongCrop == invalidated_reason:
        veselka_link = f"<https://veselka.cloud.carbonrobotics.com/admin?task_or_image_id={task_id}"
        message = f"<{veselka_link}|Task was invalidated for wrong crop."
        slack.send_slack_message(message, config.INVALID_IMAGE_ALERT_SLACK_CHANNEL_ID)

    LOG.info(f'Skipped task "{task_id}"')
    return jsonify(True)
