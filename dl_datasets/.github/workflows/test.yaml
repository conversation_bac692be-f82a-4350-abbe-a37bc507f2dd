name: Test

on:
  push:
    tags:
      - 'v*.*.*'
    branches:
      - master
  pull_request:
    branches:
      - master

env:
  PYTHON_VERSION: 3.10.6
  PYTHONPATH: ${{ github.workspace }}

jobs:

  test:
    name: Test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout
        uses: actions/checkout@v3
      
      - name: Set up Python ${{env.PYTHON_VERSION}}
        uses: actions/setup-python@v4
        with:
          python-version: ${{env.PYTHON_VERSION}}

      - name: Install Dependencies
        run: |
          pip install -r requirements.txt

      - name: Run Tests
        run:  make test
