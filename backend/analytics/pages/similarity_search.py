import logging
from typing import Optional, List

import panel
import numpy as np
import torch
import pandas as pd

from server.db import get_session, tables

LOG = logging.getLogger(__name__)


def cosine_similarity_normed_inputs(query_tensor: torch.Tensor, db_tensors: torch.Tensor) -> torch.Tensor:
    """Calculate cosine similarity between normalized tensors."""
    return torch.mm(query_tensor, db_tensors.t())


def find_similar_embeddings(
    query_embedding: List[float],
    model_id: str,
    k: int = 10,
    similarity_threshold: float = 0.5
) -> List[dict]:
    """Find k most similar embeddings to the query embedding."""
    session = get_session()

    try:
        # Get all embeddings for the model
        embeddings_query = session.query(tables.Embedding).filter(
            tables.Embedding.model_id == model_id
        ).all()

        if not embeddings_query:
            return []

        # Convert to tensors
        query_tensor = torch.tensor(query_embedding, dtype=torch.float32).unsqueeze(0)
        query_tensor = torch.nn.functional.normalize(query_tensor, p=2, dim=1)

        db_embeddings = []
        embedding_metadata = []

        for emb in embeddings_query:
            if emb.embedding and len(emb.embedding) == len(query_embedding):
                db_embeddings.append(emb.embedding)
                embedding_metadata.append({
                    'point_id': emb.point_id,
                    'model_id': emb.model_id,
                    'type': emb.type,
                    'embedding_id': emb.id
                })

        if not db_embeddings:
            return []

        db_tensor = torch.tensor(db_embeddings, dtype=torch.float32)
        db_tensor = torch.nn.functional.normalize(db_tensor, p=2, dim=1)

        # Calculate cosine similarities
        similarities = cosine_similarity_normed_inputs(query_tensor, db_tensor).squeeze().cpu().numpy()

        # Filter by threshold and get top k
        valid_indices = np.where(similarities >= similarity_threshold)[0]
        if len(valid_indices) == 0:
            return []

        # Sort by similarity (descending)
        sorted_indices = valid_indices[np.argsort(-similarities[valid_indices])]
        top_k_indices = sorted_indices[:k]

        results = []
        for idx in top_k_indices:
            result = embedding_metadata[idx].copy()
            result['similarity'] = float(similarities[idx])
            results.append(result)

        return results
    finally:
        session.close()


def get_point_details(point_id: str) -> Optional[dict]:
    """Get details about a point including image information."""
    session = queries.get_session()
    
    point = session.query(tables.Point).filter(tables.Point.id == point_id).first()
    if not point:
        return None
    
    image = session.query(tables.Image).filter(tables.Image.id == point.image_id).first()
    
    return {
        'point_id': point.id,
        'x': point.x,
        'y': point.y,
        'confidence': point.confidence,
        'category_class_name': point.category_class_name,
        'hit_class': point.hit_class.value if point.hit_class else None,
        'image_id': image.id if image else None,
        'image_url': image.url if image else None,
        'timestamp': image.timestamp_ms if image else None,
    }


def format_search_results(results: List[dict]) -> panel.Column:
    """Format search results into a displayable panel."""
    if not results:
        return panel.Column(panel.pane.Markdown("No similar embeddings found."))
    
    # Get point details for each result
    detailed_results = []
    for result in results:
        point_details = get_point_details(result['point_id'])
        if point_details:
            combined = {**result, **point_details}
            detailed_results.append(combined)
    
    if not detailed_results:
        return panel.Column(panel.pane.Markdown("No valid points found for the similar embeddings."))
    
    # Create DataFrame for display
    df_data = []
    for result in detailed_results:
        veselka_link = f"https://veselka.cloud.carbonrobotics.com/admin?image_id={result.get('image_id', '')}"
        df_data.append({
            'Similarity': f"{result['similarity']:.4f}",
            'Point ID': result['point_id'],
            'Category': result.get('category_class_name', 'N/A'),
            'Hit Class': result.get('hit_class', 'N/A'),
            'Confidence': f"{result.get('confidence', 0):.3f}",
            'Position': f"({result.get('x', 0)}, {result.get('y', 0)})",
            'Veselka Link': veselka_link
        })
    
    df = pd.DataFrame(df_data)
    
    return panel.Column(
        panel.pane.Markdown(f"## Found {len(detailed_results)} similar embeddings"),
        panel.pane.DataFrame(df, sizing_mode="stretch_width"),
        sizing_mode="stretch_width"
    )


def perform_search(
    embedding_input: str, 
    model_id: str, 
    k: int, 
    similarity_threshold: float
) -> panel.Column:
    """Perform similarity search and return formatted results."""
    if not embedding_input.strip() or not model_id.strip():
        return panel.Column(panel.pane.Markdown("Please provide both embedding values and model ID."))
    
    try:
        # Parse embedding input (expecting comma-separated values)
        embedding_values = [float(x.strip()) for x in embedding_input.split(',')]
        
        if len(embedding_values) == 0:
            return panel.Column(panel.pane.Markdown("Please provide valid embedding values."))
        
        # Perform search
        results = find_similar_embeddings(
            query_embedding=embedding_values,
            model_id=model_id,
            k=k,
            similarity_threshold=similarity_threshold
        )
        
        return format_search_results(results)
        
    except ValueError as e:
        return panel.Column(panel.pane.Markdown(f"Error parsing embedding values: {str(e)}"))
    except Exception as e:
        LOG.error(f"Error in similarity search: {str(e)}")
        return panel.Column(panel.pane.Markdown(f"Error performing search: {str(e)}"))


def get_page() -> panel.Column:
    """Main page function for similarity search."""
    panel.extension(loading_spinner="dots", loading_color="#1e52d6")
    panel.param.ParamMethod.loading_indicator = True
    
    # Input widgets
    embedding_input = panel.widgets.TextAreaInput(
        placeholder="Enter embedding values (comma-separated)...",
        sizing_mode="stretch_width",
        height=100
    )
    model_id_input = panel.widgets.TextInput(
        placeholder="Enter model ID...",
        sizing_mode="stretch_width"
    )
    k_input = panel.widgets.IntInput(
        name="Number of results (k)",
        value=10,
        start=1,
        end=100,
        sizing_mode="stretch_width"
    )
    threshold_input = panel.widgets.FloatInput(
        name="Similarity threshold",
        value=0.5,
        start=0.0,
        end=1.0,
        step=0.1,
        sizing_mode="stretch_width"
    )
    
    search_button = panel.widgets.Button(
        name="Search Similar Embeddings",
        button_type="primary",
        sizing_mode="stretch_width"
    )
    
    # Bind search function to button
    search_results = panel.bind(
        perform_search,
        embedding_input,
        model_id_input,
        k_input,
        threshold_input,
        watch=False
    )
    
    # Trigger search when button is clicked
    def trigger_search(event):
        search_results.trigger()
    
    search_button.on_click(trigger_search)
    
    # Layout
    page = panel.Column(
        panel.Spacer(max_height=50, sizing_mode="stretch_both"),
        panel.Row(
            panel.Spacer(max_width=200, sizing_mode="stretch_both"),
            panel.Column(
                panel.pane.Markdown("# Similarity Search"),
                panel.pane.Markdown(
                    """
                    Search for embeddings similar to a given query embedding using cosine similarity.
                    
                    **Instructions:**
                    1. Enter embedding values as comma-separated numbers
                    2. Provide the model ID to search within
                    3. Set the number of results to return (k)
                    4. Set the minimum similarity threshold (0.0 to 1.0)
                    5. Click "Search Similar Embeddings"
                    """
                ),
                panel.Row(
                    panel.Column(
                        embedding_input,
                        model_id_input,
                        sizing_mode="stretch_width"
                    ),
                    panel.Column(
                        k_input,
                        threshold_input,
                        sizing_mode="stretch_width"
                    ),
                    sizing_mode="stretch_width"
                ),
                search_button,
                search_results,
                sizing_mode="stretch_width",
                max_width=1400,
                min_width=800,
            ),
            panel.Spacer(max_width=200, sizing_mode="stretch_both"),
            sizing_mode="stretch_width",
            align=("center", "center"),
        ),
    )
    
    return page
