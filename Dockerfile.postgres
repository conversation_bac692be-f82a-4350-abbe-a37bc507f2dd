FROM postgres:13
LABEL org.opencontainers.image.source=https://github.com/carbonrobotics/veselka-postgres

RUN apt-get update && apt-get install -y locales \
 && sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen \
 && locale-gen

ENV LANG=en_US.utf8
ENV LC_ALL=en_US.utf8

RUN apt-get update && apt-get install -y \
    git build-essential postgresql-server-dev-13 \
 && git clone --branch v0.8.0 https://github.com/pgvector/pgvector.git \
 && cd pgvector && make && make install \
 && cd .. && rm -rf pgvector \
 && apt-get remove -y git build-essential postgresql-server-dev-13 \
 && apt-get autoremove -y && apt-get clean