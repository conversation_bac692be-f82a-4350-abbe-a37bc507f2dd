import os

from typing import Optional

from sqlalchemy import create_engine
from alembic.config import Config
from alembic import command

from ..base import BaseDataset


class ComparisonDataset(BaseDataset):
    def __init__(self, filepath: str, migration_id: Optional[str] = None):
        super().__init__(filepath, migration_id=migration_id)
        
        self._db_url = f"sqlite:///{self._filepath}"
        self._engine = create_engine(self._db_url, echo=True)        
        
        # Validate we have the migration we want
        script_location = f"{os.path.dirname(os.path.abspath(__file__))}/alembic"
        
        config = Config()        
        config.set_main_option("script_location", script_location)
        config.set_main_option("sqlalchemy.url", self._db_url)
        
        if migration_id is None:
            command.upgrade(config, "head")
        else:
            command.upgrade(config, migration_id)
        
        
        # Touch the file if it doesn't already exist
        with self._engine.connect() as connection:
            pass
        
