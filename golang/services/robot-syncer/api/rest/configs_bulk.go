package rest

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/carbonrobotics/crgo/carbon"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"

	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/audit"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
)

/**
 * Bulk update configs REST endpoints are unique in that they expect protojson as input.
 * The reason for this is to allow pattern matching on multiple nodes across multiple robots'
 * configs. For example, turn on auto_brightness for all cameras across a set of robots.
 *
 * The logic of the bulk update adds as little complexity as possible. It simply loops through
 * the serials in the order they were provided and calls the config cache to update each
 * robot's config. The order of Operations acted upon matches the order provided in the request.
 * When wildcard selectors are expanded, the new paths are added in alphabetical order.
 **/
func BulkUpdateConfigsHandler(auditLogger *audit.Logger, configCache ConfigCache, streamCache StreamCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		startTime := time.Now()
		errorPrefix := "failed to bulk update configs"
		input := robot_syncer.BulkEditRequest{}
		jsonBody, err := ctx.GetRawData()
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid BulkEditRequest -- no data provided")
			return
		}

		err = protojson.UnmarshalOptions{AllowPartial: true, DiscardUnknown: true}.Unmarshal(jsonBody, &input)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid BulkEditRequest")
			return
		}

		if len(input.Serials) == 0 {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "must provide at least one serial")
			return
		}

		if len(input.Operations) == 0 {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "must provide at least one operation")
			return
		}

		if input.Timestamp == nil {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "must provide a timestamp")
			return
		}

		if input.UserId == "" {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "must provide a user id")
			return
		}

		validSerials, err := parseSerials(input.Serials)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid serials")
			return
		}

		robotEditRecords := make([]*robot_syncer.RobotEditRecord, 0)
		for _, serial := range validSerials {
			serialStartTime := time.Now()
			robotEditRecord := &robot_syncer.RobotEditRecord{
				Serial:  serial.String(),
				Records: make([]*robot_syncer.EditRecord, 0),
			}

			records, err := configCache.BulkEditConfig(ctx, cache.BulkEditConfigParameters{
				AuditLogger: auditLogger,
				Notifier:    streamCache,
				Serial:      serial,
				Timestamp:   uint64(input.Timestamp.AsTime().UnixMilli()),
				UserID:      input.UserId,
				Operations:  input.Operations,
			})
			if err != nil {
				// if top level error, ignore key level records and continue
				robotEditRecord.Message = err.Error()
				robotEditRecords = append(robotEditRecords, robotEditRecord)
				continue
			}

			robotEditRecord.Records = records
			robotEditRecords = append(robotEditRecords, robotEditRecord)
			bulkUpdateNumConfigUpdatesBySerial.WithLabelValues(serial.String()).Set(float64(len(records)))
			bulkUpdateBySerialDurationMs.WithLabelValues(serial.String()).Set(float64(time.Since(serialStartTime).Milliseconds()))
		}

		bulkUpdateDurationMs.Set(float64(time.Since(startTime).Milliseconds()))

		response := &robot_syncer.BulkEditResponse{Robots: robotEditRecords}
		ReturnProtoJSON(ctx, response, errorPrefix)
	}
}

func parseSerials(serials []string) ([]*carbon.ValidSerial, error) {
	if len(serials) == 0 {
		return nil, fmt.Errorf("must provide at least one serial")
	}

	validSerials := make([]*carbon.ValidSerial, 0, len(serials))
	for _, serial := range serials {
		validSerial, err := carbon.ParseSerial(serial)
		if err != nil {
			return nil, err
		}
		validSerials = append(validSerials, validSerial)
	}
	return validSerials, nil
}

func RestoreConfigToVersionHandler(auditLogger *audit.Logger, configCache ConfigCache, streamCache StreamCache) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		errorPrefix := "failed to restore config to version"
		startTime := time.Now()
		input := struct {
			CopyFrom  string `json:"copyFrom"`
			Serial    string `json:"serial"`
			Timestamp int64  `json:"timestamp"`
			UserID    string `json:"userId"`
		}{}

		err := ctx.Bind(&input)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, "invalid input")
			return
		}

		serial, err := carbon.ParseSerial(input.Serial)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, fmt.Sprintf("invalid serial: %s", input.Serial))
			return
		}

		copyFromSerial, err := carbon.ParseSerial(input.CopyFrom)
		if err != nil {
			ReturnError(ctx, http.StatusBadRequest, err, errorPrefix, fmt.Sprintf("invalid copyFrom serial: %s", input.CopyFrom))
			return
		}

		if serial.Class() != copyFromSerial.Class() {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "serials must be of the same class")
			return
		}

		ts := time.UnixMilli(input.Timestamp)
		if time.Since(ts) > time.Hour*24*30 || time.Now().Before(ts) {
			ReturnError(ctx, http.StatusBadRequest, nil, errorPrefix, "timestamp must be within the last month")
			return
		}

		// fetch version from S3 that was active at the given timestamp for the given serial
		target, err := configCache.GetConfigVersion(ctx, copyFromSerial, ts)
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to get version")
			return
		}

		editRecords, err := configCache.RestoreConfigToVersion(ctx, cache.RestoreConfigToVersionParameters{
			AuditLogger: auditLogger,
			Notifier:    streamCache,
			Serial:      serial,
			Timestamp:   uint64(time.Now().UnixMilli()),
			UserID:      input.UserID,
			Target:      target,
		})
		if err != nil {
			ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to restore config")
			return
		}

		restoreConfigToVersionDurationMs.Set(float64(time.Since(startTime).Milliseconds()))
		response := &robot_syncer.BulkEditResponse{Robots: []*robot_syncer.RobotEditRecord{
			{
				Serial:  serial.String(),
				Records: editRecords,
			},
		}}
		ReturnProtoJSON(ctx, response, errorPrefix)
	}
}
