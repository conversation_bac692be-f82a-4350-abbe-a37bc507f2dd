package grpc

import (
	"context"
	"fmt"
	"path"

	"github.com/google/uuid"

	"github.com/carbonrobotics/protos/golang/generated/proto/almanac"
	"github.com/carbonrobotics/protos/golang/generated/proto/category_profile"
	"github.com/carbonrobotics/protos/golang/generated/proto/frontend"
	"github.com/carbonrobotics/protos/golang/generated/proto/robot_syncer"
	"github.com/carbonrobotics/protos/golang/generated/proto/target_velocity_estimator"
	"github.com/carbonrobotics/protos/golang/generated/proto/thinning"
	"github.com/carbonrobotics/protos/golang/generated/proto/util"

	rosy "github.com/carbonrobotics/cloud/golang/pkg/robot_syncer"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/backend/utils"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/cache"
	"github.com/carbonrobotics/cloud/golang/services/robot-syncer/clients/portal"

	log "github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/reflect/protoreflect"
)

type ProfileCache interface {
	GetCustomerProfile(profileId uuid.UUID) (*rosy.Profile, bool)
	GetCustomerProfiles(customerId uuid.UUID, profileType *frontend.ProfileType) ([]*rosy.Profile, error)
	GetProfileSyncData(customerId uuid.UUID) (*robot_syncer.GetProfileSyncDataResponse, error)
	SaveCustomerProfile(profile *rosy.Profile) error
	DeleteCustomerProfile(profileId uuid.UUID) error
}

type ProfileSyncService struct {
	robot_syncer.UnimplementedRoSyProfileSyncServiceServer // Embed the unimplemented server

	portalClient *portal.Client
	profileCache ProfileCache //*cache.ProfileCache
}

func NewProfileSyncService(server *grpc.Server, portalClient *portal.Client, profileCache *cache.ProfileCache) *ProfileSyncService {
	s := &ProfileSyncService{
		portalClient: portalClient,
		profileCache: profileCache,
	}

	robot_syncer.RegisterRoSyProfileSyncServiceServer(server, s)
	return s
}

/** START BOILERPLATE CODE **/

func (service *ProfileSyncService) GetProfileSyncData(ctx context.Context, req *robot_syncer.GetProfileSyncDataRequest) (*robot_syncer.GetProfileSyncDataResponse, error) {
	serial := req.RobotSerial
	if serial == "" {
		return nil, status.Errorf(codes.InvalidArgument, "missing robot serial")
	}

	customerId, err := service.portalClient.GetCustomerUuid(ctx, serial)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to fetch customer id %s from portal: %v", req.RobotSerial, err)
	}

	resp, err := service.profileCache.GetProfileSyncData(customerId)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to get profile sync data: %v", err)
	}

	return resp, nil
}

func (service *ProfileSyncService) UploadProfile(ctx context.Context, req *robot_syncer.UploadProfileRequest) (*util.Empty, error) {
	serial := req.RobotSerial
	if serial == "" {
		return nil, status.Errorf(codes.InvalidArgument, "missing robot serial")
	}

	customerId, err := service.portalClient.GetCustomerUuid(ctx, serial)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to fetch customer id %s from portal: %v", req.RobotSerial, err)
	}

	profile, err := DeSerializeProfileFromProto(customerId, req)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "failed to deserialize profile: %v", err)
	}

	if err = service.profileCache.SaveCustomerProfile(profile); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to save profile: %v", err)
	}

	return &util.Empty{}, nil
}

func (service *ProfileSyncService) GetProfile(ctx context.Context, req *robot_syncer.GetProfileRequest) (*robot_syncer.GetProfileResponse, error) {
	id, err := utils.RequestIDToUUID(req.GetUuid())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid profile id %s: %v", req.GetUuid(), err)
	}

	profile, ok := service.profileCache.GetCustomerProfile(id)
	if !ok {
		return nil, status.Errorf(codes.Internal, "profile %s not found", id)
	}

	if profile.Deleted {
		return nil, status.Errorf(codes.Internal, "category %s was deleted", id)
	}

	serializedProfile, err := SerializeProfileToProto(profile)
	if err != nil {
		return nil, err
	}

	return serializedProfile, nil
}

func (service *ProfileSyncService) DeleteProfile(ctx context.Context, req *robot_syncer.DeleteProfileRequest) (*util.Empty, error) {
	id, err := utils.RequestIDToUUID(req.GetUuid())
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid profile id %s: %v", req.GetUuid(), err)
	}

	if err := service.profileCache.DeleteCustomerProfile(id); err != nil {
		return nil, status.Errorf(codes.Internal, "failed to delete profile %s: %v", id, err)

	}

	return &util.Empty{}, nil
}

func (service *ProfileSyncService) PurgeProfile(ctx context.Context, req *robot_syncer.PurgeProfileRequest) (*util.Empty, error) {
	// Now that profiles are shared across all robots per customer, purging is not an
	// operation that should be done by the robot. Instead, RoSy looks at all profiles
	// during its cache initialization and purges any profiles that are marked deleted
	// and have not been updated in over a year.
	return &util.Empty{}, status.Errorf(codes.Unimplemented, "purge profile not implemented")
}

/** END BOILERPLATE CODE **/

func DeSerializeProfileFromProto(customerId uuid.UUID, req *robot_syncer.UploadProfileRequest) (*rosy.Profile, error) {
	var id string
	var profileJson string
	var profileType frontend.ProfileType
	var protected bool
	var err error
	switch profile := req.Profile.(type) {
	case *robot_syncer.UploadProfileRequest_Almanac:
		id = profile.Almanac.Id
		profileJson, err = protoMarshal(profile.Almanac)
		profileType = frontend.ProfileType_ALMANAC
		protected = profile.Almanac.Protected
	case *robot_syncer.UploadProfileRequest_Discriminator:
		id = profile.Discriminator.Id
		profileJson, err = protoMarshal(profile.Discriminator)
		profileType = frontend.ProfileType_DISCRIMINATOR
		protected = profile.Discriminator.Protected
	case *robot_syncer.UploadProfileRequest_Banding:
		id = profile.Banding.Uuid
		profileJson, err = protoMarshal(profile.Banding)
		profileType = frontend.ProfileType_BANDING
		// protected = profile.Banding.Protected FIXME add to profile when we move it to RoSy
	case *robot_syncer.UploadProfileRequest_Thinning:
		id = profile.Thinning.Id
		profileJson, err = protoMarshal(profile.Thinning)
		profileType = frontend.ProfileType_THINNING
		// protected = profile.Thinning.Protected FIXME
	case *robot_syncer.UploadProfileRequest_Modelinator:
		id = path.Join(profile.Modelinator.ModelId, profile.Modelinator.CropId, req.RobotSerial)
		profileJson, err = protoMarshal(profile.Modelinator)
		profileType = frontend.ProfileType_MODELINATOR
		// protected = profile.Modelinator.Protected FIXME
	case *robot_syncer.UploadProfileRequest_TargetVelocityEstimator:
		id = profile.TargetVelocityEstimator.Id
		profileJson, err = protoMarshal(profile.TargetVelocityEstimator)
		profileType = frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR
		protected = profile.TargetVelocityEstimator.Protected
	case *robot_syncer.UploadProfileRequest_CategoryCollection:
		id = profile.CategoryCollection.Id
		profileJson, err = protoMarshal(profile.CategoryCollection)
		profileType = frontend.ProfileType_CATEGORY_COLLECTION
		protected = profile.CategoryCollection.Protected
	case *robot_syncer.UploadProfileRequest_Category:
		id = profile.Category.Id
		profileJson, err = protoMarshal(profile.Category)
		profileType = frontend.ProfileType_CATEGORY
		protected = profile.Category.Protected
	default:
		err = status.Errorf(codes.InvalidArgument, "profile type %v unimplemented", profile)
	}
	if err != nil {
		return nil, err
	}

	uuid, err := utils.RequestIDToUUID(id)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "invalid profile id %s: %v", id, err)
	}

	return &rosy.Profile{
		CustomerId:  customerId,
		Id:          uuid,
		ProfileType: profileType,
		Profile:     profileJson,
		Protected:   protected,
		UpdatedAt:   req.LastUpdateTimeMs,
		Deleted:     false,
	}, nil
}

func protoMarshal(m protoreflect.ProtoMessage) (string, error) {
	b, err := protojson.Marshal(m)
	if err != nil {
		return "", err
	}
	return string(b), nil
}

func SerializeProfileToProto(profile *rosy.Profile) (*robot_syncer.GetProfileResponse, error) {
	response := &robot_syncer.GetProfileResponse{}
	if profile == nil {
		return nil, fmt.Errorf("no profile")
	}
	err := error(nil)
	switch profile.ProfileType {
	case frontend.ProfileType_ALMANAC:
		almanac := &almanac.AlmanacConfig{}
		err = protojson.Unmarshal([]byte(profile.Profile), almanac)
		response.Profile = &robot_syncer.GetProfileResponse_Almanac{Almanac: almanac}
	case frontend.ProfileType_DISCRIMINATOR:
		discriminator := &almanac.DiscriminatorConfig{}
		err = protojson.Unmarshal([]byte(profile.Profile), discriminator)
		response.Profile = &robot_syncer.GetProfileResponse_Discriminator{Discriminator: discriminator}
	case frontend.ProfileType_BANDING:
		bandingDef := &frontend.BandingDef{}
		err = protojson.Unmarshal([]byte(profile.Profile), bandingDef)
		response.Profile = &robot_syncer.GetProfileResponse_Banding{Banding: bandingDef}
	case frontend.ProfileType_THINNING:
		thinningDef := &thinning.ConfigDefinition{}
		err = protojson.Unmarshal([]byte(profile.Profile), thinningDef)
		response.Profile = &robot_syncer.GetProfileResponse_Thinning{Thinning: thinningDef}
	case frontend.ProfileType_MODELINATOR:
		modelinator := &almanac.ModelinatorConfig{}
		err = protojson.Unmarshal([]byte(profile.Profile), modelinator)
		response.Profile = &robot_syncer.GetProfileResponse_Modelinator{Modelinator: modelinator}
	case frontend.ProfileType_TARGET_VELOCITY_ESTIMATOR:
		targetVelocityEstimator := &target_velocity_estimator.TVEProfile{}
		err = protojson.Unmarshal([]byte(profile.Profile), targetVelocityEstimator)
		response.Profile = &robot_syncer.GetProfileResponse_TargetVelocityEstimator{TargetVelocityEstimator: targetVelocityEstimator}
	case frontend.ProfileType_CATEGORY_COLLECTION:
		categoryCollection := &category_profile.CategoryCollection{}
		err = protojson.Unmarshal([]byte(profile.Profile), categoryCollection)
		response.Profile = &robot_syncer.GetProfileResponse_CategoryCollection{CategoryCollection: categoryCollection}
	case frontend.ProfileType_CATEGORY:
		category := &category_profile.Category{}
		err = protojson.Unmarshal([]byte(profile.Profile), category)
		response.Profile = &robot_syncer.GetProfileResponse_Category{Category: category}
	default:
		response = nil
		err = fmt.Errorf("get profile type %v unimplemented", profile.ProfileType)
	}
	if err != nil {
		log.WithError(err).Warnf("ProfileSync: bad value while unmarshaling profile %v, value=%v", profile.Id, profile.Profile)
	}
	return response, err
}
