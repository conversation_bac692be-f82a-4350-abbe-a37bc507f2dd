import panel as pn

class Page:
    def __init__(self) -> None:
        pass

    def render(self) -> pn.Column:
        """Main page function for similarity search."""
        pn.extension(loading_spinner="dots", loading_color="#1e52d6")
        
        # Layout
        page = pn.Column(
            pn.<PERSON>r(max_height=50, sizing_mode="stretch_both"),
            pn.Row(
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                pn.Column(
                    pn.pane.Markdown("# Similarity Search"),
                    pn.pane.Markdown(
                        """
                        This page will allow searching for similar embeddings.
                        
                        **Coming soon:**
                        - Search by embedding vectors
                        - Find nearest neighbors
                        - Filter by similarity threshold
                        - Display results with links to Veselka
                        """
                    ),
                    sizing_mode="stretch_width",
                    max_width=1600,
                    min_width=360,
                ),
                pn.Spacer(max_width=400, sizing_mode="stretch_both"),
                sizing_mode="stretch_width",
                align=("center", "center"),
            ),
        )
        
        return page


def get_page() -> pn.Column:
    page = Page()

    return pn.Column(
        pn.pane.Markdown("### Similarity Search Prototype"),
        page.render(),
        sizing_mode="stretch_width",
        width_policy="max",
    )