package rest

import (
	"encoding/json"
	"fmt"
	"net/http"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
)

func ReturnError(context *gin.Context, status int, err error, errorPrefix string, errorMessage string) {
	var fullMessage string
	if errorPrefix == "" {
		fullMessage = errorMessage
	} else {
		fullMessage = fmt.Sprintf("%s: %s", errorPrefix, errorMessage)
	}

	if err == nil {
		log.Error(fullMessage)
	} else {
		log.WithError(err).Error(fullMessage)
	}
	context.JSON(status, gin.H{"error": fullMessage})
}

// ReturnProtoJSON serializes the given protobuf message as JSON and sends it
// as an HTTP 200 response, or sends an HTTP 500 response with an error message
// if serialization fails. It always writes to the response body, and thus
// should generally be the last call in a request handler.
func ReturnProtoJSON(ctx *gin.Context, msg proto.Message, errorPrefix string) error {
	encoded, err := protojson.Marshal(msg)
	if err != nil {
		ReturnError(ctx, http.StatusInternalServerError, err, errorPrefix, "failed to marshal response proto")
		return err
	}
	ctx.JSON(http.StatusOK, json.RawMessage(encoded))
	return nil
}
