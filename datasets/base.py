from typing import Optional

from sqlalchemy import create_engine

class BaseDataset:
    def __init__(self, filepath: str, migration_id: Optional[str] = None):
        self._filepath = filepath
        self._migration_id = migration_id
        
    @property
    def filepath(self) -> str:
        return self._filepath

    def get_session(self):
        raise NotImplementedError("get_session not implemented")