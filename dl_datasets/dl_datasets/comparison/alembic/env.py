# Note that this file isn't actually part of the package structure.
# We need to do an explicit import in order to get Base from the tables
# file because we also reference this file from the alembic command api.

from alembic import context
from dl_datasets.comparison.tables import Base
from sqlalchemy import engine_from_config, pool

target_metadata = Base.metadata

config = context.config
section = config.config_ini_section

config_section = config.get_section(config.config_ini_section)

assert config_section is not None

engine = engine_from_config(
    config_section,
    prefix="sqlalchemy.",
    poolclass=pool.NullPool,
)

with engine.connect() as connection:
    context.configure(
        connection=connection,
        target_metadata=target_metadata,
        render_as_batch=True,
        compare_type=True,
    )

    with context.begin_transaction():
        context.run_migrations()
